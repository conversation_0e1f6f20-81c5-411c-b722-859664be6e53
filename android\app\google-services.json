{"project_info": {"project_number": "801228927819", "project_id": "chainivo-2f990", "storage_bucket": "chainivo-2f990.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:801228927819:android:c37ff64a89e7e48a9faece", "android_client_info": {"package_name": "com.innotech.chainivo"}}, "oauth_client": [{"client_id": "801228927819-7k3nh88uqb59sm1d9d2uifcif79e5lo0.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.innotech.chainivo", "certificate_hash": "5e8f16062ea3cd2c4a0d547876baa6f38cabf625"}}, {"client_id": "801228927819-ah51tc5h9lk824dvt9geigji69rcq31k.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.innotech.chainivo", "certificate_hash": "24155b8ebd98611aa741ab9848bda0e5ebfaf384"}}, {"client_id": "801228927819-j06k5kbmp4sliucoibgg92i7ksbq60bc.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBbf_lQP4oUfiesfouLmBLMTOLwqMjpKYw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "801228927819-j06k5kbmp4sliucoibgg92i7ksbq60bc.apps.googleusercontent.com", "client_type": 3}, {"client_id": "801228927819-72ei193mugu5vvau03tk04e791jqmibt.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.innotech.chanivo"}}]}}}], "configuration_version": "1"}