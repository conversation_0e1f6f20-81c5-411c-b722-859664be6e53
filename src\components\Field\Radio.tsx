import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export const RadioAction = () => {
  return (
    <View>
      <View style={styles.radio}>
        <View style={styles.radioSelected} />
      </View>
    </View>
  );
};

export const Radio = () => {
  return (
    <View>
      <View style={styles.radio}></View>
    </View>
  );
};
const styles = StyleSheet.create({
  radio: {
    width: 20,
    height: 20,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  radioSelected: {
    backgroundColor: '#007AFF',
    width: 10,
    height: 10,
    borderRadius: 50,
    margin: 'auto',
  },
});
