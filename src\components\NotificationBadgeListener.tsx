import React, { useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useDispatch } from 'react-redux';
import { NotificationActions } from '../redux/reducers/notificationReducer';
import { useNotificationBadgeCount } from '../redux/hook/notificationHook';

interface NotificationBadgeListenerProps {
  children?: React.ReactNode;
}

/**
 * Component that listens for badge count changes and syncs with the app state
 * This component should be placed high in the component tree (e.g., in App.tsx)
 */
const NotificationBadgeListener: React.FC<NotificationBadgeListenerProps> = ({ children }) => {
  const dispatch = useDispatch();
  const badgeCount = useNotificationBadgeCount();

  useEffect(() => {
    // Initial badge sync when component mounts
    NotificationActions.setBadge(dispatch);
    
    console.log('🔢 [BadgeListener] Initialized with badge count:', badgeCount);
  }, [dispatch]);

  useEffect(() => {
    // Listen for app state changes
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('📱 [BadgeListener] App state changed to:', nextAppState);
      
      if (nextAppState === 'active') {
        // App became active, sync badge count
        console.log('🔄 [BadgeListener] App became active, syncing badge count');
        NotificationActions.syncBadgeWithUnreadCount(dispatch);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [dispatch]);

  useEffect(() => {
    // Log badge count changes
    console.log('🔢 [BadgeListener] Badge count changed to:', badgeCount);
  }, [badgeCount]);

  // This component doesn't render anything visible
  return <>{children}</>;
};

export default NotificationBadgeListener;

// Hook for components that need to monitor badge changes
export const useBadgeListener = () => {
  const dispatch = useDispatch();
  const badgeCount = useNotificationBadgeCount();

  const syncBadge = () => {
    NotificationActions.setBadge(dispatch);
  };

  const syncWithUnreadCount = () => {
    NotificationActions.syncBadgeWithUnreadCount(dispatch);
  };

  const incrementBadge = () => {
    NotificationActions.incrementBadge(dispatch);
  };

  const decrementBadge = () => {
    NotificationActions.decrementBadge(dispatch);
  };

  const clearBadge = () => {
    NotificationActions.clearBadge(dispatch);
  };

  return {
    badgeCount,
    syncBadge,
    syncWithUnreadCount,
    incrementBadge,
    decrementBadge,
    clearBadge,
  };
};
