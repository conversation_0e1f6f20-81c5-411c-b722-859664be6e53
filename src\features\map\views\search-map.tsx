import {useState} from 'react';
import {MapItem} from '../da';
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faAngleLeft,
  faLocationDot,
  faStar,
} from '@fortawesome/free-solid-svg-icons';
import {CardMapSearchSkeleton} from '../local-component/map-item-shimmer';
import {TypoSkin} from '../../../assets/skin/typography';
import {TextField} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import ConfigAPI from '../../../Config/ConfigAPI';

type SearchMapAutocompleteProps = {
  suggestList?: Array<MapItem>;
  title: string;
  onChangeMapPicker: () => void;
  onSelectLocation: (location: MapItem) => void;
  onClose: () => void;
};

export default function SearchMapAutocomplete({
  onSelectLocation,
  onClose,
  onChangeMapPicker,
  title,
  suggestList,
}: SearchMapAutocompleteProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<MapItem[]>([]);

  const handleSearch = async (inputText: string) => {
    setIsLoading(true);
    const GEOCODING_API_URL = ConfigAPI.getAddressByGoogleKey(inputText);
    try {
      const response = await fetch(GEOCODING_API_URL);
      const data = await response.json();

      setSearchResults(data['results']);
    } catch (error) {
      console.error(error);
    }
    setIsLoading(false);
  };

  const handleSelectLocation = (location: MapItem) => {
    onSelectLocation(location);
    setSearchResults([]);
  };

  return (
    <View style={styles.searchContainer}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <TouchableOpacity
          style={{paddingVertical: 12, paddingHorizontal: 16}}
          onPress={() => {
            onClose();
          }}>
          <FontAwesomeIcon icon={faAngleLeft} size={24} color="#00204D99" />
        </TouchableOpacity>
        <Text style={[TypoSkin.title3, {flex: 1}]}>{title}</Text>
        <View style={{flexDirection: 'row', gap: 4, paddingHorizontal: 8}}>
          {/* <TouchableOpacity
            style={{flexDirection: 'row', padding: 6}}
            onPress={onChangeMapPicker}>
            <Text
              style={[
                TypoSkin.title5,
                {color: ColorThemes.light.primary_main_color},
              ]}>
              Tìm trên bản đồ
            </Text>
          </TouchableOpacity> */}
        </View>
      </View>
      <View style={{paddingHorizontal: 16, marginBottom: 16}}>
        <TextField
          style={{
            height: 36,
            padding: 8,
            lineHeight: 22,
          }}
          placeholder={title}
          onChange={e => {
            if (e.trim().length === 0) {
              setSearchResults([]);
              return;
            }
            if (e.trim().length && e.trim().length % 3 === 0) {
              handleSearch(e);
            }
          }}
          onSubmit={handleSearch}
        />
      </View>

      {isLoading ? (
        <ActivityIndicator
          size={'large'}
          color={ColorThemes.light.primary_main_color}
          style={{
            height: '80%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        />
      ) : null}
      {searchResults?.length > 0 ? (
        <View
          style={{
            backgroundColor: ColorThemes.light.white,
            flex: 1,
          }}>
          <FlatList
            data={searchResults}
            showsVerticalScrollIndicator={true}
            style={{flex: 1}}
            keyExtractor={(item, i) => `${i} ${item.place_id}`}
            contentContainerStyle={{flexGrow: 1}}
            renderItem={({item, index}) => (
              <TouchableOpacity
                key={`key ${index}`}
                onPress={() => {
                  handleSelectLocation(item);
                }}
                style={styles.resultItemContainer}>
                <FontAwesomeIcon
                  icon={faLocationDot}
                  size={20}
                  color={ColorThemes.light.primary_main_color}
                />
                <View style={{flex: 1}}>
                  <Text style={styles.resultItem}>{item.name}</Text>
                  <Text style={TypoSkin.subtitle3} numberOfLines={1}>
                    {item.formatted_address}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            ListEmptyComponent={() => {
              return [1, 2, 3].map(item => (
                <CardMapSearchSkeleton key={item} />
              ));
            }}
          />
        </View>
      ) : null}

      {/* default view  */}
      {!searchResults.length ? (
        <View style={{flex: 1, paddingVertical: 16}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              gap: 8,
              paddingBottom: 10,
            }}>
            <FontAwesomeIcon
              icon={faStar}
              size={20}
              color={ColorThemes.light.primary_main_color}
            />
            <Text style={[TypoSkin.body3]}>Tìm kiếm địa điểm để chọn</Text>
          </View>
          {/* đề xuất danh sách */}
          {/* <FlatList
                data={suggestList}
                style={{ flex: 1 }}
                keyExtractor={(item, i) => `${i} ${item}`}
                renderItem={({ item }) => (
                    <TouchableOpacity onPress={() => { handleSelectLocation(item) }} style={styles.resultItemContainer}>
                        <FontAwesomeIcon icon={faLocationDot} size={20} color={ColorThemes.light.primary_main_color} />
                        <View>
                            <Text style={styles.resultItem} >
                                {item.name}
                            </Text>
                            <Text style={TypoSkin.subtitle3} >
                                {item.formatted_address}
                            </Text>
                        </View>
                    </TouchableOpacity>
                )}
                ListEmptyComponent={() => {
                    return [1, 2, 3].map(item => <CardMapSearchSkeleton key={item} />);
                }}
            /> */}
        </View>
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({
  searchContainer: {
    flex: 1,
    width: '100%',
    paddingTop: 20,
    backgroundColor: ColorThemes.light.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  input: {
    height: 36,
    padding: 8,
    fontSize: 16,
    lineHeight: 22,
  },
  resultItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    flex: 1,
    borderBottomColor: ColorThemes.light.neutral_main_background_color,
    borderBottomWidth: 1,
    backgroundColor: ColorThemes.light.white,
    gap: 8,
  },
  resultItem: {
    ...TypoSkin.title3,
  },
});
