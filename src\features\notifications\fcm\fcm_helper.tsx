import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {PermissionsAndroid, Platform} from 'react-native';
import {navigate, navigationRef, RootScreen} from '../../../router/router';
import {getApp, initializeApp} from '@react-native-firebase/app';
import {getAuth} from '@react-native-firebase/auth';
import {NotificationActions} from '../../../redux/reducers/notificationReducer';
import store from '../../../redux/store/store';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';

// Define the CustomGlobal type
interface CustomGlobal {
  RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS: boolean;
  RNFB_MODULAR_DEPRECATION_STRICT_MODE: boolean;
}

declare var globalThis: CustomGlobal;

globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;
globalThis.RNFB_MODULAR_DEPRECATION_STRICT_MODE = true;

// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: 'AIzaSyC2fTM2N7fKelqgp9NUN87meUV7XUH_JV4',
  authDomain: 'chainivo-2f990.firebaseapp.com',
  projectId: 'chainivo-2f990',
  storageBucket: 'chainivo-2f990.firebasestorage.app',
  messagingSenderId: '801228927819',
  appId: '1:801228927819:web:5a99dae333c7fc429faece',
  measurementId: 'G-T2WG4KRMEG',
};

// Initialize Firebase
let initFirebase;
try {
  initFirebase = getApp(); // This will get the default app if it's already initializeduccncx
  getAuth(initFirebase);
} catch (error) {
  initFirebase = initializeApp(firebaseConfig); // Initialize app with the provided config
}

export const initNotificationPermission = async () => {
  await registerAppWithFCM();

  const authStatus = await messaging().requestPermission();

  // ANDROID
  if (Platform.OS === 'android') {
    try {
      await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      ).then(response => {
        if (response) {
          return getFcmToken();
        }
      });
      // requestNotificationPermission();
    } catch (err) {
      console.warn('requestNotificationPermission error: ', err);
    }
  } else {
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    // Request permissions (required for iOS)
    const settings = await notifee.requestPermission();
    if (settings.authorizationStatus === AuthorizationStatus.DENIED) {
      console.log('User denied permissions request');
    } else if (
      settings.authorizationStatus === AuthorizationStatus.AUTHORIZED
    ) {
      // console.log('User granted permissions request');
    } else if (
      settings.authorizationStatus === AuthorizationStatus.PROVISIONAL
    ) {
      console.log('User provisionally granted permissions request');
    }
    if (enabled) {
      return getFcmToken();
    }
  }
};

//method was called to listener events from firebase for notification triger
// main hàm xử lý thông tin và logic
export function registerListenerWithFCM() {
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    console.log(
      '📱 [FCM] onMessage Received:',
      JSON.stringify(remoteMessage, null, 2),
    );
    if (
      remoteMessage?.notification?.title &&
      remoteMessage?.notification?.body
    ) {
      // Increment badge count
      await NotificationActions.incrementBadge(store.dispatch);

      // show notification
      onDisplayNotification(
        remoteMessage.notification?.title,
        remoteMessage.notification?.body,
        remoteMessage?.data,
      );
    }
  });

  // Handle notification events when app is in foreground or background but visible
  notifee.onForegroundEvent(({type, detail}) => {
    console.log('🔔 [FCM] onForegroundEvent:', {
      type: EventType[type],
      notificationId: detail.notification?.id,
      data: detail.notification?.data,
      pressAction: detail.pressAction,
    });

    switch (type) {
      case EventType.DISMISSED:
        handleNotificationDismissed(detail);
        break;
      case EventType.PRESS:
        handleNotificationPressed(detail, 'foreground');
        break;
      case EventType.ACTION_PRESS:
        handleNotificationActionPressed(detail, 'foreground');
        break;
      default:
        console.log(
          '🔔 [FCM] Unhandled foreground event type:',
          EventType[type],
        );
    }
  });

  // Handle notification events when app is in background or quit
  notifee.onBackgroundEvent(async ({type, detail}) => {
    console.log('🔔 [FCM] onBackgroundEvent:', {
      type: EventType[type],
      notificationId: detail.notification?.id,
      data: detail.notification?.data,
      pressAction: detail.pressAction,
    });

    switch (type) {
      case EventType.DISMISSED:
        await handleNotificationDismissed(detail);
        break;
      case EventType.PRESS:
        await handleNotificationPressed(detail, 'background');
        break;
      case EventType.ACTION_PRESS:
        await handleNotificationActionPressed(detail, 'background');
        break;
      default:
        console.log(
          '🔔 [FCM] Unhandled background event type:',
          EventType[type],
        );
    }
  });

  // Handle notification when app is opened from background
  messaging().onNotificationOpenedApp(async remoteMessage => {
    console.log(
      '📱 [FCM] onNotificationOpenedApp:',
      JSON.stringify(remoteMessage, null, 2),
    );
    await handleFirebaseNotificationOpened(remoteMessage, 'background');
  });

  // Check whether an initial notification is available (app opened from quit state)
  messaging()
    .getInitialNotification()
    .then(async remoteMessage => {
      if (remoteMessage) {
        console.log(
          '📱 [FCM] getInitialNotification (quit state):',
          JSON.stringify(remoteMessage, null, 2),
        );
        await handleFirebaseNotificationOpened(remoteMessage, 'quit');
      }
    });

  return unsubscribe;
}

// Handle notification dismissed
const handleNotificationDismissed = async (detail: any) => {
  console.log('❌ [FCM] Notification dismissed:', detail.notification?.id);
  await NotificationActions.decrementBadge(store.dispatch);
};

// Handle notification pressed
const handleNotificationPressed = async (
  detail: any,
  context: 'foreground' | 'background',
) => {
  // Use the centralized notification click handler
  await NotificationActions.handleNotificationClick(
    store.dispatch,
    detail.notification,
    context,
  );

  // Navigate to notification screen
  navigate(RootScreen.NotifCommunity);
};

// Handle notification action button pressed
const handleNotificationActionPressed = async (
  detail: any,
  context: 'foreground' | 'background',
) => {
  console.log(`🎯 [FCM] Notification action pressed in ${context}:`, {
    notificationId: detail.notification?.id,
    actionId: detail.pressAction?.id,
    data: detail.notification?.data,
  });

  await NotificationActions.decrementBadge(store.dispatch);

  // Handle different action types
  switch (detail.pressAction?.id) {
    case 'mark_read':
      console.log('📖 [FCM] Mark as read action');
      // Handle mark as read
      break;
    case 'reply':
      console.log('💬 [FCM] Reply action');
      // Handle reply action
      break;
    default:
      console.log('🎯 [FCM] Default action - navigate to app');
      store.dispatch(CustomerActions.getInfor());
      navigate(RootScreen.NotifCommunity);
  }
};

// Handle Firebase notification opened (from onNotificationOpenedApp or getInitialNotification)
const handleFirebaseNotificationOpened = async (
  remoteMessage: any,
  context: 'background' | 'quit',
) => {
  // Use the centralized notification click handler
  await NotificationActions.handleNotificationClick(
    store.dispatch,
    {
      id: remoteMessage.messageId,
      data: remoteMessage.data,
      notification: remoteMessage.notification,
    },
    context,
  );

  // Navigate to notification screen
  navigate(RootScreen.NotifCommunity);
};

//method was called to get FCM tiken for notification
export const getFcmToken = async () => {
  let token = '';
  try {
    token = await messaging().getToken();
    console.log('getFcmToken-->', Platform.OS, token);
    await AsyncStorage.setItem('fcmToken', token);
  } catch (error) {
    console.log('getFcmToken Device Token error ', error);
  }
  return token;
};

//method was called on  user register with firebase FCM for notification
export async function registerAppWithFCM() {
  if (!messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .registerDeviceForRemoteMessages()
      .then(status => {
        console.log('registerDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('registerDeviceForRemoteMessages error ', error);
      });
  }
  messaging().subscribeToTopic('all-devices');
}

//method was called on un register the user from firebase for stoping receiving notifications
export async function unRegisterAppWithFCM() {
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );

  if (messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .unregisterDeviceForRemoteMessages()
      .then(status => {
        console.log('unregisterDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('unregisterDeviceForRemoteMessages error ', error);
      });
  }
  await messaging().deleteToken();
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );
}

export const decrementBadgeCount = async () => {
  await NotificationActions.decrementBadge(store.dispatch);
};

// Badge management functions
export const incrementBadgeCount = async () => {
  await NotificationActions.incrementBadge(store.dispatch);
};

export const setBadgeCount = async (count: number) => {
  try {
    await notifee.setBadgeCount(count);
    await NotificationActions.setBadge(store.dispatch);
    console.log(`🔢 [FCM] Badge count set to: ${count}`);
  } catch (error) {
    console.error('Error setting badge count:', error);
  }
};

export const clearBadgeCount = async () => {
  await NotificationActions.clearBadge(store.dispatch);
};

export const getBadgeCount = async (): Promise<number> => {
  try {
    return await notifee.getBadgeCount();
  } catch (error) {
    console.error('Error getting badge count:', error);
    return 0;
  }
};

// Listen for badge count changes
export const listenToBadgeChanges = () => {
  // This function can be called to set up badge count monitoring
  console.log('🔢 [FCM] Badge change listener initialized');

  // You can add additional badge monitoring logic here
  // For example, periodic badge count sync
  setInterval(async () => {
    try {
      await NotificationActions.setBadge(store.dispatch);
    } catch (error) {
      console.error('Error syncing badge count:', error);
    }
  }, 30000); // Sync every 30 seconds
};

//method was called to display notification
async function onDisplayNotification(
  title: string,
  body: string,
  data:
    | {
        [key: string]: string | number | object;
      }
    | any
    | undefined,
) {
  // Create a channel (required for Android)
  const channelId = await notifee.createChannel({
    id: 'default',
    name: 'Default Channel',
  });

  // Display a notification
  await notifee.displayNotification({
    title: title,
    body: body,
    data: data,
    android: {
      channelId,
      importance: AndroidImportance.HIGH,
      // pressAction is needed if you want the notification to open the app when pressed
      pressAction: {
        id: 'default',
      },
    },
  });
}
