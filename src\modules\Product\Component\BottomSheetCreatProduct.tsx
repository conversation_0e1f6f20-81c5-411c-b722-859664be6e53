import React, {forwardRef, useEffect, useState, useMemo} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Pressable,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {hideBottomSheet} from 'wini-mobile-components';
import ListItemLable from './LabelProduct';
import ListItem from './ListProductCreate';
import ScreenHeader from '../../../Screen/Layout/header';
import {getImage} from '../../../redux/actions/rootAction';
import {BottomSheetCreatProductStyles} from '../styles/BottomSheetCreatProductStyles';
import {ProductDA} from '../productDA';
export const BottomSheetCreatProduct = forwardRef(
  function BottomSheetCreatProduct(
    data: {
      handleSelect?: any;
      handleSelectLabel?: any;
      handleSelectFrom?: any;
      type: any;
    },
    ref: any,
  ) {
    const {type, handleSelect, handleSelectLabel} = data;
    const [selecChildID, setSelecChildID] = useState<string>('');
    const [selecChildName, setSelecChildName] = useState<string>('');
    const [dataLabel, setDataLabel] = useState<any[]>();
    const [dataProduct, setDataProduct] = useState<any>();
    const [searchData, setSearchData] = useState<string>('');
    const [selectItemChild, setSelectItemChild] = useState<any>();
    const [backSelect, setBackSelect] = useState<boolean>(false);
    const [processedDataLabel, setProcessedDataLabel] = useState<any[]>([]);
    const productDA = new ProductDA();

    const {data: dataCategory} = useSelector(
      (state: RootState) => state.category,
    );

    // Process data with images
    useEffect(() => {
      const processDataLabel = async () => {
        if (dataLabel) {
          try {
            const data = await getImage({items: dataLabel});
            setProcessedDataLabel(data || []);
          } catch (error) {
            console.error('Error processing data label:', error);
            setProcessedDataLabel([]);
          }
        } else {
          setProcessedDataLabel([]);
        }
      };
      processDataLabel();
    }, [dataLabel]);

    // Filter data based on search
    const filteredDataLabel = useMemo(() => {
      if (!processedDataLabel || !searchData.trim())
        return processedDataLabel || [];
      return processedDataLabel.filter((item: any) =>
        item.Name?.toLowerCase().includes(searchData.toLowerCase()),
      );
    }, [processedDataLabel, searchData]);

    const filteredDataCategory = useMemo(() => {
      if (!dataCategory || !searchData.trim()) return dataCategory || [];
      return dataCategory.filter((item: any) =>
        item.Name?.toLowerCase().includes(searchData.toLowerCase()),
      );
    }, [dataCategory, searchData]);
    const handleSubmit = (selecChildID: string, selecChildName: string) => {
      if (!selecChildID || !selecChildName) {
        return;
      }
      if (selecChildID) {
        if (type === 'label') {
          handleSelectLabel({
            id: selecChildID,
            name: selecChildName,
          });
          hideBottomSheet(ref);
        } else if (type === 'Product') {
          handleSelect({
            id: selecChildID,
            name: selecChildName,
          });
          hideBottomSheet(ref);
        }
      }
    };
    const callApiBrand = async () => {
      let respone = await productDA.getAllBrand();
      if (respone && respone?.code == 200) {
        setDataLabel(respone?.data);
      }
    };

    const callApiCategory = async () => {
      let respone = await productDA.getAllCategory();
      if (respone && respone?.code == 200) {
        let arrayData = respone?.data?.map((item: any) => {
          return {
            ...item,
            Customer: respone?.Customer?.find(
              (customer: any) => customer.Id == item.CustomerId,
            ),
            parent: respone?.Parent?.find(
              (parent: any) => parent.Id == item.ParentId,
            ),
          };
        });
        setDataProduct(arrayData);
      }
    };
    useEffect(() => {
      if (type === 'label') {
        callApiBrand();
      }
      if (type === 'Product') {
        callApiCategory();
      }
    }, [type]);
    const handleBackSelectLabel = () => {
      setSelectItemChild(null);
      setSelecChildID('');
      setSelecChildName('');
      setBackSelect(true);
    };
    return (
      <Pressable style={BottomSheetCreatProductStyles.header}>
        <ScreenHeader
          onBack={handleBackSelectLabel}
          title={type && type == 'label' ? `Chọn thương hiệu` : `Chọn danh mục`}
        />
        <KeyboardAvoidingView
          style={{flex: 1, position: 'relative'}}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust for iOS and Android
          keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0} // Offset for iOS
        >
          <View style={BottomSheetCreatProductStyles.search}>
            <View style={BottomSheetCreatProductStyles.searchContent}>
              <TextInput
                style={BottomSheetCreatProductStyles.searchInput}
                placeholder="Search"
                value={searchData}
                onChange={e => setSearchData(e.nativeEvent.text)}
              />
              <TouchableOpacity onPress={() => setSearchData('')}>
                <Text style={BottomSheetCreatProductStyles.searchCancelButton}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={BottomSheetCreatProductStyles.bodySheet}>
            {type == 'label' ? (
              <>
                <ListItemLable
                  setSelecChildID={setSelecChildID}
                  setSelecChildName={setSelecChildName}
                  dataLabel={filteredDataLabel}
                />
                {searchData.trim() && filteredDataLabel?.length === 0 && (
                  <View style={BottomSheetCreatProductStyles.searchData}>
                    <Text
                      style={BottomSheetCreatProductStyles.notFoundData}></Text>
                  </View>
                )}
              </>
            ) : null}
            {type == 'Product' ? (
              <>
                <ListItem
                  setSelecChildID={setSelecChildID}
                  setSelecChildName={setSelecChildName}
                  selectItemChild={selectItemChild}
                  setSelectItemChild={setSelectItemChild}
                  dataCategory={filteredDataCategory}
                  backSelect={backSelect}
                  setBackSelect={setBackSelect}
                />
                {searchData.trim() && filteredDataCategory.length === 0 && (
                  <View style={BottomSheetCreatProductStyles.searchData}>
                    <Text
                      style={BottomSheetCreatProductStyles.notFoundData}></Text>
                  </View>
                )}
              </>
            ) : null}
            <Pressable style={{flex: 1}}></Pressable>
          </View>
          <View style={BottomSheetCreatProductStyles.footerSheet}>
            <View style={BottomSheetCreatProductStyles.cancelSheetButton}>
              <TouchableOpacity
                style={BottomSheetCreatProductStyles.cancelButton}
                onPress={() => hideBottomSheet(ref)}>
                <Text style={BottomSheetCreatProductStyles.buttonText}>
                  Đóng
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={BottomSheetCreatProductStyles.confirmButton}
                onPress={() => handleSubmit(selecChildID, selecChildName)}>
                <Text style={BottomSheetCreatProductStyles.buttonTextConfirm}>
                  Xác nhận
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Pressable>
    );
  },
);
export default BottomSheetCreatProduct;
