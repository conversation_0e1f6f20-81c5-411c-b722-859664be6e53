import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
const DropdownViolation = (props: {
  setShowDropdown(type: boolean): void;
  setOpenModal(type: boolean): void;
  handleGoback(): void;
}) => {
  return (
    <View
      style={{
        position: 'absolute',
        top: 100,
        right: 16,
        backgroundColor: 'white',
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        zIndex: 1000,
        minWidth: 170,
      }}>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 12,
          paddingHorizontal: 16,
          borderBottomWidth: 1,
          borderBottomColor: ColorThemes.light.neutral_main_border_color,
        }}
        onPress={() => props.handleGoback()}>
        <View style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
          <AppSvg SvgSrc={iconSvg.home} size={20} />
          <Text
            style={{
              ...TypoSkin.buttonText2,
              color:
                ColorThemes.light.neutral_absolute_reverse_background_color,
              fontWeight: '500',
            }}>
            Trở về trang chủ
          </Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 12,
          paddingHorizontal: 16,
        }}
        onPress={() => {
          props.setShowDropdown(false);
          props.setOpenModal(true);
        }}>
        <View style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
          <AppSvg SvgSrc={iconSvg.report} size={20} />
          <Text
            style={{
              ...TypoSkin.buttonText2,
              color:
                ColorThemes.light.neutral_absolute_reverse_background_color,
              fontWeight: '500',
            }}>
            Báo cáo vi phạm
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};
export default DropdownViolation;
