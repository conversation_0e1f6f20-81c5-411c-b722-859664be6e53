import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { RootScreen } from '../../../router/router';
import { Title } from '../../../Config/Contanst';
import MenuProduct from './MenuProduct';
import ManageProductDetail from '../list/ListManageProductDetail';
import { ManageProductStyles } from '../styles/ManageProductStyles';
const ManageItemProduct = ({ data }: { data: any }) => {
  const navigation = useNavigation<any>();
  const productInfo = data;
  const [menu, setMenu] = useState<string>('Còn hàng');
  return (
    <View style={ManageProductStyles.containerScreenManageProduct}>
      <MenuProduct setMenu={setMenu} menu={menu} data={productInfo} />
      <ManageProductDetail menu={menu} dataShop={productInfo} />
      <TouchableOpacity
        style={ManageProductStyles.ScreenManageProductButton}
        onPress={() =>
          navigation.push(RootScreen.CreateNewProduct, {
            title: Title.CreateMyProduct,
          })
        }>
        <Text style={ManageProductStyles.ScreenManageProductTextButton}>Thêm sản phẩm mới</Text>
      </TouchableOpacity>
    </View>
  );
};
export default ManageItemProduct;
