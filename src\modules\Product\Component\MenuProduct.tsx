import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { ManageProductStyles } from '../styles/ManageProductStyles';
import { MenuProductProps } from '../types';
const MenuProduct = (props: MenuProductProps) => {
    const { menu, setMenu, data } = props;
    const TypeMenuPorduct = ["Còn hàng", "Hết hàng", "Chờ duyệt", "Vi phạm", "Ẩn"]
    return (
        <View style={ManageProductStyles.HeaderMenuManageProduct}>
            {data && data.length > 0 && data.map((item, index) => (
                < TouchableOpacity style={ManageProductStyles.tabMenuManageProduct} onPress={() => setMenu(item.name)}>
                    <Text style={menu == TypeMenuPorduct[index] ? ManageProductStyles.textActionMenuManageProduct : ManageProductStyles.textMenuManageProduct}>{item.name}</Text>
                    <Text style={menu == TypeMenuPorduct[index] ? ManageProductStyles.textActionMenuManageProduct : ManageProductStyles.textMenuManageProduct}>({item.number})</Text>
                </TouchableOpacity>
            ))}
        </View>
    );
};
export default MenuProduct;