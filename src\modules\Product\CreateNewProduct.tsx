/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useRoute } from '@react-navigation/native';
import CreateProduct from './form/CreateProduct';
import { InforHeader } from '../../Screen/Layout/headers/inforHeader';
import { productStyles } from './styles/CreateNewProductStyles';

const CreateNewProduct = () => {
  const route = useRoute<any>();
  const [title, setTitle] = useState<string>('');
  useEffect(() => {
    if (route?.params?.title) {
      setTitle(route.params.title);
    }
  }, [route.params.title]);
  return (
    <View style={productStyles.containerCreateNewProduct}>
      <InforHeader title={title} />
      <CreateProduct routeParams={route.params} />
    </View>
  );
};
export default CreateNewProduct;
