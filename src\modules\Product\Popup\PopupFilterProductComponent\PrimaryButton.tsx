import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';

interface PrimaryButtonProps {
  title: string;
  onPress: () => void;
}

const PrimaryButton = ({title, onPress}: PrimaryButtonProps) => (
  <TouchableOpacity style={styles.primaryButton} onPress={onPress}>
    <Text style={styles.primaryButtonText}>{title}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  primaryButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: 'center',
    flex: 1,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PrimaryButton;
