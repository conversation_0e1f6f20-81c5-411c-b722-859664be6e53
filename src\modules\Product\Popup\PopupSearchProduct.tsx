import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
} from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import CustomDropdown from '../../../components/CustomDropdown';

interface PopupSearchProductProps {
  isVisible: boolean;
  onClose: () => void;
  onSearch: (data: { name: string; stock: string; price: string }) => void;
}

const priceOptions = [
  { id: '1', name: 'Dưới 1,000,000đ' },
  { id: '2', name: '1,000,000đ - 5,000,000đ' },
  { id: '3', name: '5,000,000đ - 10,000,000đ' },
  { id: '4', name: 'Trên 10,000,000đ' },
];

const iStockOptions = [
  { id: '1', name: 'Dưới 100 sản phẩm' },
  { id: '2', name: 'Từ 100 - 500 sản phẩm' },
  { id: '3', name: 'Từ 500 - 1000 sản phẩm' },
  { id: '4', name: 'Trên 1000 sản phẩm' },
];

const PopupSearchProduct: React.FC<PopupSearchProductProps> = ({
  isVisible,
  onClose,
  onSearch,
}) => {
  const [name, setName] = useState('');
  const [stock, setStock] = useState('');
  const [price, setPrice] = useState('');

  const handleSearch = () => {
    onSearch({ name, stock, price });
    setName('');
    setStock('');
    setPrice('');
  };

  const handleClose = () => {
    setName('');
    setStock('');
    setPrice('');
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.dialogContainer}>
          <Text style={styles.title}>Tìm kiếm sản phẩm</Text>

          <TextInput
            style={styles.input}
            placeholder="Tên sản phẩm"
            placeholderTextColor={ColorThemes.light.neutral_text_subtitle_color}
            value={name}
            onChangeText={setName}
          />
          <CustomDropdown
            placeholder="Tồn kho"
            options={iStockOptions}
            value={stock}
            onValueChange={selectedStock => setStock(selectedStock.toString())}
          />
          <CustomDropdown
            placeholder="Giá tiền"
            options={priceOptions}
            value={price}
            onValueChange={selectedPrice => setPrice(selectedPrice.toString())}
          />

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={handleClose}>
              <Text style={styles.buttonText}>Huỷ</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={handleSearch}>
              <Text style={[styles.buttonText, styles.confirmButtonText]}>
                Tìm kiếm
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: '85%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D4E2F3',
    paddingHorizontal: 16,
    borderRadius: 8,
    height: 50,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  confirmButton: {
    backgroundColor: ColorThemes.light.infor_main_color,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: 'white',
  },
});

export default PopupSearchProduct;
