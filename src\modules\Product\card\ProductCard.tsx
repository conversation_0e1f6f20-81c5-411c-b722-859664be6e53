import React from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {Product} from '../../../redux/models/product';
import {Ultis} from '../../../utils/Utils';
import {useTranslation} from 'react-i18next';

const {width} = Dimensions.get('window');

// <PERSON><PERSON>ch thước mặc định của card sản phẩm
const DEFAULT_ITEM_WIDTH = width * 0.45;
const DEFAULT_ITEM_HEIGHT = 320;

interface ProductCardProps {
  item: Product;
  onPress: (item: Product) => void;
  onAddToCart: (item: Product) => void;
  onFavoritePress?: (item: Product) => void;
  width?: number;
  height?: number;
}

const ProductCard: React.FC<ProductCardProps> = React.memo(
  ({
    item,
    onPress,
    onAddToCart,
    onFavoritePress,
    width: cardWidth = DEFAULT_ITEM_WIDTH,
    height: cardHeight = DEFAULT_ITEM_HEIGHT,
  }) => {
    const isSmallCard = cardWidth < 170;
    const hasDiscount = item.Discount && item.Discount > 0;

    const handleAddToCart = () => {
      onAddToCart(item);
    };

    const handleFavoritePress = () => {
      if (onFavoritePress) onFavoritePress(item);
    };

    return (
      <TouchableOpacity
        style={[styles.itemContainer, {width: cardWidth, height: cardHeight}]}
        onPress={() => onPress(item)}
        activeOpacity={0.8}>
        {/* Ảnh sản phẩm */}
        <View style={styles.imageContainer}>
          <FastImage
            source={{uri: item.Img}}
            style={styles.image}
            resizeMode={FastImage.resizeMode.cover}
          />

          {/* Badge giảm giá */}
          {hasDiscount ? (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>{item.Discount}%</Text>
            </View>
          ) : null}

          {/* Logo thương hiệu */}
          <View style={styles.brandLogo} />
        </View>

        {/* Thông tin sản phẩm */}
        <View style={styles.infoContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {item.Name || ''}
          </Text>

          <View>
            <View style={styles.priceContainer}>
              {hasDiscount ? (
                <View style={styles.discountedPriceContainer}>
                  <Text style={styles.price}>
                    {Ultis.money(
                      item.Price - (item.Price * (item.Discount ?? 0)) / 100,
                    )}
                    đ
                  </Text>
                  <Text style={styles.originalPrice}>
                    {Ultis.money(item.Price ?? 0)} đ
                  </Text>
                </View>
              ) : (
                <Text style={styles.price}>
                  {Ultis.money(item.Price || 0)} đ
                </Text>
              )}
            </View>
            <View style={styles.bottomRow}>
              <View
                style={[
                  styles.ratingContainer,
                  isSmallCard && styles.smallCardRatingContainer,
                ]}>
                <View style={styles.ratingInfo}>
                  <Winicon
                    src="fill/user interface/star"
                    size={14}
                    color={ColorThemes.light.warning_main_color}
                  />
                  <Text style={styles.ratingText}>
                    {item.rating?.toFixed(1)}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.soldText,
                    isSmallCard && styles.smallCardSoldText,
                  ]}>
                  {`${
                    isSmallCard ? '' : '| '
                  }Đã bán ${Ultis.formatNumberConvert(item?.Sold ?? 0)}`}
                </Text>
              </View>
              <View style={styles.actionButtons}>
                {onFavoritePress && item && (
                  <TouchableOpacity
                    style={styles.iconButton}
                    onPress={handleFavoritePress}
                    activeOpacity={0.7}>
                    <Winicon
                      src="outline/emoticons/heart"
                      size={15}
                      color={
                        item.IsFavorite
                          ? ColorThemes.light.error_main_color
                          : ColorThemes.light.neutral_text_subtitle_color
                      }
                    />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={[styles.iconButton, {marginLeft: 6}]}
                  onPress={handleAddToCart}
                  activeOpacity={0.7}>
                  <Winicon
                    src="outline/shopping/cart"
                    size={15}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  },
);

const styles = StyleSheet.create({
  itemContainer: {
    borderRadius: 10,
    backgroundColor: ColorThemes.light.white,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    marginBottom: 16,
    elevation: 2,
    shadowColor: ColorThemes.light.neutral_absolute_reverse_background_color,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  imageContainer: {
    width: '100%',
    flex: 1,
    padding: 8,
    position: 'relative',
    backgroundColor: ColorThemes.light.white,
  },
  image: {
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    borderRadius: 8,
    backgroundColor: ColorThemes.light.white,
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: ColorThemes.light.secondary5_main_color,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: ColorThemes.light.white,
    fontWeight: 'bold',
    fontSize: 12,
  },
  brandLogo: {
    position: 'absolute',
    top: 4,
    right: 4,
    padding: 4,
    borderRadius: 4,
    alignItems: 'center',
  },
  infoContainer: {
    height: 120,
    justifyContent: 'space-between',
    padding: 8,
  },
  title: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  discountedPriceContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  price: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  originalPrice: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_placeholder_color,
    textDecorationLine: 'line-through',
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  smallCardRatingContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  ratingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.warning_main_color,
    marginLeft: 4,
  },
  soldText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 4,
  },
  smallCardSoldText: {
    marginLeft: 0,
    marginTop: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 26,
    height: 26,
    backgroundColor: ColorThemes.light.neutral_bolder_background_color,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ProductCard;
