import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Text,
  Image,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {ListTile, Rating, Winicon} from 'wini-mobile-components';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {useRoute} from '@react-navigation/native';
import {DataController} from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import RenderHTML from 'react-native-render-html';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';
import {ScrollView} from 'react-native-gesture-handler';
import FastImage from 'react-native-fast-image';
import ClickableImage from '../../../components/ClickableImage';
import {navigate, RootScreen} from '../../../router/router';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

const RatingScreen = () => {
  const [reviews, setReviews] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const route = useRoute<any>();
  const shopId = route.params?.shopId;
  const productId = route.params?.id;
  const rate = route.params?.rate;
  const countRate = route.params?.countRate;
  const ratingController = new DataController('Rating');
  const width = Dimensions.get('window').width;
  const pageSize = 10;

  // Fetch reviews data
  useEffect(() => {
    fetchReviews(1, true);
  }, []);

  const fetchReviews = async (page: number = 1, isRefresh: boolean = false) => {
    if (isRefresh) {
      setLoading(true);
      setCurrentPage(1);
      setHasMore(true);
    } else {
      setLoadingMore(true);
    }

    const res = await ratingController.getPatternList({
      page: page,
      size: pageSize,
      query: shopId ? `@ShopId: {${shopId}}` : `@ProductId: {${productId}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
        OrderId: ['Id', 'Code'],
        ProductId: ['Id', 'Name', 'Description', 'Img'],
      },
    });

    if (res.code === 200) {
      // map again res.data with customer
      const mappedData = res.data.map((item: any) => {
        return {
          ...item,
          products: res.Product?.filter(
            (product: any) => product.Id === item.ProductId,
          ),
          userName: res.Customer.find(
            (customer: any) => customer.Id == item.CustomerId,
          ).Name,
          userAvatar: `${
            ConfigAPI.urlImg +
            res.Customer.find((customer: any) => customer.Id == item.CustomerId)
              .AvatarUrl
          }`,
          date: Ultis.numberToTime(item.DateCreated, true),
        };
      });

      if (isRefresh) {
        setReviews(mappedData);
      } else {
        setReviews(prev => [...prev, ...mappedData]);
      }

      // Check if there are more items to load
      setHasMore(mappedData.length === pageSize);
      setCurrentPage(page);
    }

    setLoading(false);
    setRefreshing(false);
    setLoadingMore(false);
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchReviews(1, true);
  }, []);

  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchReviews(currentPage + 1, false);
    }
  }, [loadingMore, hasMore, currentPage]);

  // Skeleton placeholder for review items
  const renderSkeletonItem = () => (
    <View style={styles.listTile}>
      <SkeletonPlaceholder borderRadius={4}>
        <View style={{flexDirection: 'row'}}>
          <View style={{width: 40, height: 40, borderRadius: 20}} />
          <View style={{marginLeft: 12, flex: 1}}>
            <View style={{width: 120, height: 16, marginBottom: 8}} />
            <View style={{width: 100, height: 12, marginBottom: 8}} />
            <View style={{width: '100%', height: 14, marginBottom: 4}} />
            <View style={{width: '80%', height: 14, marginBottom: 8}} />
            <View style={{width: 80, height: 12}} />
          </View>
        </View>
      </SkeletonPlaceholder>
    </View>
  );

  // Render loading footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator
          size="small"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  };

  // Render stars based on rating
  const renderStars = (rating: number) => {
    return (
      <View style={styles.starsContainer}>
        <Rating value={rating} size={20} fillColor="#FFC043" />
      </View>
    );
  };

  // Render review item
  const renderReviewItem = ({item}: {item: any}) => (
    <ListTile
      key={item.id}
      leading={
        <View
          style={[
            styles.avatarContainer,
            item.userAvatar && {
              backgroundColor: ColorThemes.light.neutral_main_background_color,
            },
          ]}>
          {item.userAvatar ? (
            <Image
              source={{uri: item.userAvatar}}
              style={{width: 40, height: 40, borderRadius: 20}}
            />
          ) : (
            <Text style={styles.avatarText}>
              {item.userName?.charAt(0) || ''}
            </Text>
          )}
        </View>
      }
      listtileStyle={{alignItems: 'flex-start'}}
      title={
        <View style={styles.reviewHeader}>
          <Text style={styles.userName}>{item.userName || ''}</Text>
          {renderStars(item.Value)}
        </View>
      }
      subtitle={
        <View style={styles.reviewContent}>
          <Text style={styles.comment}>{item.Description}</Text>
          <RenderHTML contentWidth={width} source={{html: item.Content}} />
          {/* show Img  */}
          {item.ListImg?.length > 0 && !shopId && (
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              style={{marginVertical: 8, height: 56}}
              contentContainerStyle={{flexGrow: 1, gap: 8}}>
              {item.ListImg?.length > 0 &&
                item.ListImg?.split(',')?.map(
                  (image: string, index: number) => (
                    <ClickableImage
                      key={`item-${index}`}
                      source={{uri: ConfigAPI.urlImg + image}}
                      style={{
                        width: 56,
                        height: 56,
                        borderRadius: 8,
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                      }}
                    />
                  ),
                )}
            </ScrollView>
          )}
          {item.products?.length > 0 && shopId && (
            <ScrollView
              showsVerticalScrollIndicator={false}
              style={{marginVertical: 8, height: undefined}}
              contentContainerStyle={{flexGrow: 1, gap: 8}}>
              {item.products?.length > 0 &&
                item.products?.map((product: any, index: number) => (
                  <ListTile
                    key={index}
                    style={{padding: 0}}
                    onPress={() =>
                      navigate(RootScreen.ProductDetail, {
                        id: product.Id,
                      })
                    }
                    leading={
                      <FastImage
                        source={{uri: ConfigAPI.urlImg + product.Img}}
                        style={{
                          width: 56,
                          height: 56,
                          borderRadius: 8,
                          borderColor:
                            ColorThemes.light.neutral_main_border_color,
                          borderWidth: 1,
                        }}
                      />
                    }
                    title={product.Name}
                  />
                ))}
            </ScrollView>
          )}
          <Text style={styles.date}>{item.date}</Text>
        </View>
      }
      style={styles.listTile}
    />
  );

  return (
    <View style={styles.container}>
      <InforHeader title={shopId ? 'Đánh giá cửa hàng' : 'Đánh giá sản phẩm'} />

      {/* show rating star and total review  */}
      <View
        style={{
          paddingVertical: 8,
          borderBottomWidth: 1,
          borderBottomColor: ColorThemes.light.neutral_main_border_color,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 16,
          }}>
          <View style={styles.starsContainer}>
            <Rating
              value={!isNaN(rate) && countRate > 0 ? rate : 0}
              size={20}
              fillColor="#FFC043"
            />
            <Text style={{...TypoSkin.body2, marginLeft: 8}}>
              {!isNaN(rate) && countRate > 0 ? rate.toFixed(1) : '0.0'}
            </Text>
          </View>

          <Text style={styles.reviewCount}>Tổng {countRate ?? 0} đánh giá</Text>
        </View>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          {/* Show skeleton placeholders while loading */}
          {Array.from({length: 5}).map((_, index) => (
            <View style={{width: '100%'}} key={index}>
              {renderSkeletonItem()}
            </View>
          ))}
        </View>
      ) : (
        <FlatList
          data={reviews}
          renderItem={renderReviewItem}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
            />
          }
          ListEmptyComponent={<EmptyPage title="Chưa có đánh giá nào" />}
          ListFooterComponent={renderFooter}
          onEndReached={loadMore}
          onEndReachedThreshold={0.1}
          keyExtractor={(item, index) => `review-${item.Id || index}`}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  loadingContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  listTile: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    padding: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  avatarContainer: {
    marginTop: 8,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  reviewHeader: {
    flexDirection: 'column',
    marginBottom: 4,
  },
  userName: {
    ...TypoSkin.subtitle1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 4,
    alignItems: 'center',
  },
  reviewContent: {
    marginTop: 4,
  },
  comment: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_body_color,
    marginBottom: 8,
  },
  date: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  headerContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_divider_color,
  },
  headerTitle: {
    ...TypoSkin.subtitle1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  reviewCount: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  footerLoader: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default RatingScreen;
