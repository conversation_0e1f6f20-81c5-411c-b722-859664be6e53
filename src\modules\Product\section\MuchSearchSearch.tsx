import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
  Dimensions,
} from 'react-native';
import ProductItem from '../FavoriteProductComponent/ProductItem';
import {productAction} from '../../../redux/actions/productAction';
import {Product} from '../../../redux/models/product';
import {getRandomObjects} from '../../../utils/arrayUtils';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

const {width: screenWidth} = Dimensions.get('window');

// Header Component
interface HeaderProps {
  title: string;
  onSeeMore?: () => void;
}

const Header: React.FC<HeaderProps> = ({title, onSeeMore}) => (
  <View style={styles.header}>
    <Text style={styles.headerTitle}>{title}</Text>
    <TouchableOpacity onPress={onSeeMore}>
      <Text style={styles.seeMore}>Xem thêm</Text>
    </TouchableOpacity>
  </View>
);

// Function to chunk array into groups of specified size
const chunkArray = (array: Product[], size: number): Product[][] => {
  const chunkedArray: Product[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunkedArray.push(array.slice(i, i + size));
  }
  return chunkedArray;
};

// Main Screen Component
const MostSearchedScreen: React.FC<{
  onSeeMore: () => void;
  onRefresh: boolean;
  title?: string;
  isHot?: boolean;
}> = ({title, onSeeMore, onRefresh, isHot}) => {
  const [chunkedProducts, setChunkedProducts] = useState<Product[][]>([]);

  useEffect(() => {
    getData();
  }, [onRefresh]);

  const getData = async () => {
    let data = await productAction.find({
      page: 1,
      size: 100,
      searchRaw: isHot ? `@IsHot:{${isHot}}` : undefined,
    });
    if (data.length > 15) {
      data = getRandomObjects(data, 15);
    }
    setChunkedProducts(chunkArray(data, 5));
  };

  const handleSeeMore = useCallback(() => {
    onSeeMore();
  }, [onSeeMore]);

  const renderProductPage = ({item}: {item: Product[]}) => (
    <View style={styles.productPage}>
      {item.map((product: Product) => (
        <ProductItem key={product.Id} item={product} />
      ))}
    </View>
  );

  return (
    <View style={styles.screen}>
      <Header title={title as string} onSeeMore={handleSeeMore} />
      {chunkedProducts?.length !== 0 && (
        <FlatList
          data={chunkedProducts}
          renderItem={renderProductPage}
          keyExtractor={(item, index) => `page-${index}`}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          contentContainerStyle={styles.flatListContent}
        />
      )}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  screen: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  headerTitle: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMore: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.infor_main_color,
  },
  separator: {
    height: 1,
    backgroundColor: '#EAEAEA',
    marginLeft: 96,
  },
  flatListContent: {
    paddingBottom: 16,
  },
  productPage: {
    width: screenWidth - 40,
  },
});

export default MostSearchedScreen;
