import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {productAction} from '../../../redux/actions/productAction';
import {Product} from '../../../redux/models/product';
import {updateArrayWithObjects} from '../../../utils/arrayUtils';
import {navigate, RootScreen} from '../../../router/router';
import {CartActions} from '../../../redux/reducers/CartReducer';
import {RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {favoriteProductAction} from '../../../redux/actions/favoriteProductAction';
import {Ultis} from '../../../utils/Utils';
import {TypoSkin} from '../../../assets/skin/typography';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';

const {width: screenWidth} = Dimensions.get('window');

// ProductCard component
const ProductCard = ({
  product,
  addToCart,
  onFavorite,
}: {
  product: Product;
  addToCart: (item: Product) => void;
  onFavorite: (id: string) => void;
}) => {
  const getDiscount = () => {
    if (!product.Discount || product.Discount <= 0) return null;
    return (
      <View style={styles.discountBadge}>
        <Text style={styles.discountText}>{product.Discount}%</Text>
      </View>
    );
  };

  return (
    <View style={styles.productCard}>
      <View style={styles.imageContainer}>
        <Image source={{uri: product.Img}} style={styles.productImage} />
        {getDiscount()}
      </View>
      <View style={styles.productDetails}>
        <View style={styles.productNameContainer}>
          <Text style={styles.productName} numberOfLines={1}>
            {product.Name || ''}
          </Text>
          <Text style={styles.productDescription} numberOfLines={2}>
            {product.Description || ''}
          </Text>
        </View>
        <View
          style={[
            styles.priceContainer,
            screenWidth < 375 && {alignItems: 'flex-start'},
          ]}>
          <View style={styles.prices}>
            {product.Discount && product.Discount > 0 ? (
              <View style={styles.priceGroupColumn}>
                <Text style={styles.currentPrice}>
                  {Ultis.money(
                    product.Price - (product.Price * product.Discount) / 100,
                  )}{' '}
                  đ
                </Text>
                <Text style={styles.originalPrice}>
                  {Ultis.money(product.Price ?? 0)} đ
                </Text>
              </View>
            ) : (
              <Text style={styles.currentPrice}>
                {Ultis.money(product.Price || 0)} đ
              </Text>
            )}
          </View>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.cartButton}
              onPress={() => onFavorite(product.Id)}
              activeOpacity={0.7}>
              {product.IsFavorite ? (
                <Winicon src="color/emoticons/heart" size={15} />
              ) : (
                <Winicon src="outline/emoticons/heart" size={15} color="#666" />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.cartButton, {marginLeft: 6}]}
              onPress={() => addToCart(product)}
              activeOpacity={0.7}>
              <Winicon src="outline/shopping/cart" size={15} color="#666" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

// Main ProductSection component
interface SuggestionProductSectionProps {
  title?: string;
  showSeeAll?: boolean;
  onSeeAllPress?: () => void;
  onRefresh?: boolean;
  categoryId?: string;
  isLoadmore?: boolean;
  scrollEnabled?: boolean;
  onLoadMoreEnd?: (hasMore: boolean) => void;
}

const SuggestionProductSection: React.FC<SuggestionProductSectionProps> = ({
  title = 'Gợi ý cho bạn',
  showSeeAll = true,
  onSeeAllPress,
  onRefresh = false,
  categoryId,
  isLoadmore = false,
  scrollEnabled = true,
  onLoadMoreEnd,
}) => {
  const dispatch = useDispatch();
  const customerHook = useSelectorCustomerState();
  const customer = useSelector((state: RootState) => state.customer.data);
  const [products, setProducts] = useState<Product[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const size = 5;
  const customerId = customerHook.data?.Id;

  useEffect(() => {
    setPage(1);
    setLoading(false);
    setHasMoreData(true);
    initData();
  }, [onRefresh, categoryId]);

  useEffect(() => {
    if (isLoadmore && !loading && hasMoreData) {
      loadMoreData();
    }
  }, [isLoadmore]);

  const loadMoreData = async () => {
    if (loading || !hasMoreData) return;

    setLoading(true);
    try {
      const nextPage = page + 1;
      const data = await productAction.find({
        page: nextPage,
        size: size,
        searchRaw: `@IsHot:{true}`,
      });

      if (data?.length > 0) {
        setProducts(prevProducts => [...prevProducts, ...data]);
        setPage(nextPage);
        const hasMore = data.length === size;
        setHasMoreData(hasMore);
        if (onLoadMoreEnd) onLoadMoreEnd(hasMore);
      } else {
        setHasMoreData(false);
        if (onLoadMoreEnd) onLoadMoreEnd(false);
      }
    } catch (error) {
      console.error('Error loading more products:', error);
      setHasMoreData(false);
      if (onLoadMoreEnd) onLoadMoreEnd(false);
    } finally {
      setLoading(false);
    }
  };

  const initData = async () => {
    setLoading(true);
    try {
      const params = {
        page: 1,
        size: size,
        searchRaw: `@IsHot:{true}`,
      };

      if (categoryId) params.searchRaw += `@CategoryId:{${categoryId}}`;
      let data = await productAction.find(params, customerId);
      setProducts(data);
      setHasMoreData(data.length === size);
    } catch (error) {
      console.error('Error initializing data:', error);
      setHasMoreData(false);
    } finally {
      setLoading(false);
    }
  };

  const onProductPress = (product: Product) => {
    navigate(RootScreen.ProductDetail, {id: product.Id});
  };

  const addToCart = (product: Product) => {
    CartActions.addItemToCart(product, 1)(dispatch);
  };

  const onFavoriteProduct = async (product: Product) => {
    if (!customer) return;
    try {
      const res = await favoriteProductAction.create({
        ProductId: product.Id,
        CustomerId: customer.Id,
        Name: product.Name,
      });
      if (res) {
        const productUpdate = products.find(item => item.Id === product.Id);
        if (productUpdate) {
          productUpdate.IsFavorite = true;
          const newData = updateArrayWithObjects(products, [productUpdate]);
          setProducts(newData);
        }
      }
    } catch (error) {
      console.log('onFavorite', error);
    }
  };

  if (products.length === 0 && !loading)
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <Text>Không có sản phẩm</Text>
      </View>
    );

  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{title}</Text>
        {showSeeAll && (
          <TouchableOpacity style={styles.seeAllButton} onPress={onSeeAllPress}>
            <Text style={styles.seeAllText}>Xem thêm</Text>
            <Text style={styles.seeAllIcon}>→</Text>
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={products}
        scrollEnabled={scrollEnabled}
        renderItem={({item: product}) => (
          <TouchableOpacity
            key={product.Id}
            onPress={() => onProductPress(product)}>
            <ProductCard
              product={product}
              addToCart={() => addToCart(product)}
              onFavorite={() => onFavoriteProduct(product)}
            />
          </TouchableOpacity>
        )}
        keyExtractor={item => item.Id}
        ListFooterComponent={() => {
          return isLoadmore && loading ? (
            <ActivityIndicator
              style={{marginVertical: 10}}
              size="large"
              color={ColorThemes.light.primary_main_color}
            />
          ) : null;
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    color: ColorThemes.light.primary_main_color,
    marginRight: 4,
  },
  seeAllIcon: {
    color: ColorThemes.light.primary_main_color,
    fontSize: 18,
  },
  productCard: {
    flexDirection: 'row',
    borderRadius: 8,
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
    flexShrink: 0, // Không cho phép image container bị thu nhỏ
  },
  productImage: {
    width: screenWidth < 375 ? 100 : 120, // Responsive image size
    height: screenWidth < 375 ? 100 : 120,
    borderRadius: 8,
  },
  discountBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: ColorThemes.light.secondary5_main_color,
    borderRadius: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  discountText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  productDetails: {
    flex: 1, // Sử dụng flex để chiếm phần còn lại
    marginLeft: 12,
    // justifyContent: 'space-between',
    minWidth: 0, // Cho phép shrink khi cần thiết
  },
  productName: {
    fontSize: screenWidth < 375 ? 14 : 16, // Responsive font size
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  productDescription: {
    fontSize: screenWidth < 375 ? 11 : 12,
    color: '#666',
    lineHeight: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    flexWrap: 'wrap',
  },
  prices: {
    flex: 1,
    flexWrap: 'wrap',
  },
  priceGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceGroupColumn: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  currentPrice: {
    fontSize: screenWidth < 375 ? 16 : 18,
    fontWeight: 'bold',
    color: '#ff4d4f',
  },
  originalPrice: {
    fontSize: screenWidth < 375 ? 11 : 12,
    color: '#999',
    marginTop: screenWidth < 375 ? 2 : 0,
    textDecorationLine: 'line-through',
  },
  actionButtons: {
    flexDirection: 'row',
    flexShrink: 0, // Không cho phép action buttons bị thu nhỏ
  },
  iconButton: {
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    width: screenWidth < 375 ? 32 : 36,
    height: screenWidth < 375 ? 32 : 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
  },
  icon: {
    fontSize: screenWidth < 375 ? 16 : 18,
  },
  productNameContainer: {
    justifyContent: 'flex-start',
    // marginBottom: 8,
  },
  favoriteButton: {
    // marginRight: 8,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButton: {
    width: 26,
    height: 26,
    backgroundColor: ColorThemes.light.neutral_bolder_background_color,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SuggestionProductSection;
export type {Product, SuggestionProductSectionProps};
