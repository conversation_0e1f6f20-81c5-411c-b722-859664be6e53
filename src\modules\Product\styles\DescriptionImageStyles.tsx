import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const DescriptionImageStyles = StyleSheet.create({
    section: {
        marginTop: 10,
        marginLeft: 10,
        marginRight: 10,
        backgroundColor: 'white',
        borderRadius: 5,
        padding: 10,
        elevation: 2, // Tạo bóng cho Android
        shadowColor: '#A9A9A9', // Tạo bóng cho iOS
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    detail: {
        display: 'flex',
        gap: 2,
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    image: {
        height: 73,
        width: 73,
        marginLeft: 10,
        marginRight: 10,
    },
    imageContent: {
        marginTop: 10
    },
    avata: {
        marginTop: 10,
        marginLeft: 10,
        width: 73
    },
    avataImage: {
        height: 73,
        width: 73
    },
    avataText: {
        marginTop: 10,
        margin: 'auto'
    },
    cancelImageButton: {
        position: 'absolute',
        bottom: 0,
        left: '50%',
        transform: [{ translateX: -10 }, { translateY: 1 }],
    },
    label: {
        fontSize: 14,
        fontWeight: 'bold',
        marginBottom: 5,
        color: '#333',
    },
    imagePlaceholder: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderStyle: 'dashed',
        width: 73,
        height: 73,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        marginTop: 10,
        marginLeft: 10,
    },
    placeholderText: {
        color: '#888',
        fontSize: 14,
    },
    input: {
        borderRadius: 5,
        padding: 10,
        fontSize: 14,
        color: '#555555',
    },
});
