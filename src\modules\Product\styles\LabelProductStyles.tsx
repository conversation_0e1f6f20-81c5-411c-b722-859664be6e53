import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const LabelProductStyles = StyleSheet.create({
    Container: {
        flex: 1,
        marginBottom: 50
    },
    LabelContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    LabelText: {
        color: 'black',
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    LabelImage: {
        width: 30,
        height: 30,
        borderRadius: 100
    },
    LabelName: {
        marginLeft: 10,
        fontSize: 20
    }

});
