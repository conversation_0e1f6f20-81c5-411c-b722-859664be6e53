import { StyleSheet, Dimensions } from 'react-native';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';


const { width } = Dimensions.get('window');

// Constants
export const ITEM_WIDTH = width * 0.32;
export const ITEM_SPACING = 10;

export const ListManageProductDetailStyles = StyleSheet.create({
    productList: {
        marginTop: 10,
        marginBottom: 10,
        flex: 1,
    },
    productCard: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 5,
        padding: 10,
        marginVertical: 10,
        borderBottomWidth: 0.3,
        borderColor: '#00FFFF',
    },
    productImage: {
        width: 64,
        height: 64,
        marginRight: 16,
    },
    productInfo: {
        flex: 1,
        gap: 18,
    },
    productName: {
        ...TypoSkin.title3,
        fontWeight: '500',
        marginBottom: 8,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        color: '#FFD700',
        marginRight: 5,
    },
    reviewSell: {
        flexDirection: 'row',
        display: 'flex',
        height: 60,
        borderBottomWidth: 0.3,
        borderColor: '#00FFFF',
    },
    reviews: {
        color: '#000',
    },
    statusContainer: {
        flexDirection: 'column',
        marginLeft: 20,
        gap: 11,
        alignItems: 'flex-start',
        flex: 1,
    },
    action: {
        display: 'flex',
        gap: 8,
        flexDirection: 'row',
        alignItems: 'center',
    },
    likesContainer: {
        marginTop: 5,
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 4,
    },

    likes: {
        color: '#FF0000',
    },
    switch: {
        marginHorizontal: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        display: 'flex',
        justifyContent: 'space-around',
        alignItems: 'center',
        marginTop: 10,
    },
    actionButton: {
        padding: 5,
        borderRadius: 5,
        marginVertical: 5,
        width: 100,
        alignItems: 'center',
        borderColor: '#007AFF',
        borderWidth: 1,
    },
    deleteButton: {},
    actionButtonText: {
        color: '#007AFF',
        fontSize: 12,
    },
});
