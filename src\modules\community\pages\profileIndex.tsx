import {
  Animated,
  Dimensions,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useRef, useState, useEffect} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showDialog,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import {onShare} from '../../../features/share';
import {useNavigation, useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useDispatch, useSelector} from 'react-redux';
import store, {AppDispatch, RootState} from '../../../redux/store/store';
import ImagePicker from 'react-native-image-crop-picker';
import {BaseDA} from '../../../base/BaseDA';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {CustomerRankType, FollowStatus} from '../../../Config/Contanst';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import EmptyPage from '../../../Screen/emptyPage';
import {CustomerDA} from '../../customer/da';
import {Ultis} from '../../../utils/Utils';
import {ActionSheetOption} from '../components/ActionSheet';
import ActionSheet from '../components/ActionSheet';
import About from './about';
import ClickableImage from '../../../components/ClickableImage';
import {DefaultPost, SkeletonPlaceCard} from '../card/defaultPost';
import {myFeedActions} from '../reducers/myFeedReducer';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import FastImage from 'react-native-fast-image';

export default function ProfileCommunity() {
  const [isLoading, setIsLoading] = useState(false);
  const [isRefresh, setIsRefresh] = useState(false);

  const handleMainRefresh = () => {
    setIsRefresh(true);
    getProfile();
  };
  const [activeTab, setActiveTab] = useState(0);
  const route = useRoute<any>();
  const {Id, forEschool} = route.params;
  const scrollY = useRef(new Animated.Value(0)).current;
  const customerDA = new CustomerDA();
  const [profile, setProfile] = useState<any>(null);
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  // const profile = useSelectorCustomerState().data;
  const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  // const [isFollowing, setIsFollowing] = useState(false);
  const dispatch: AppDispatch = useDispatch();
  const [checkFollowCustomer, setCheckFollowCustomer] = useState<any>(null);
  const [avt, setAvt] = useState<any>(null);
  const [currentActions, setCurrentActions] = useState<ActionSheetOption[]>([]);
  // const isFollowing = true;

  useEffect(() => {
    if (Id) {
      getProfile();
    }
  }, [Id, isRefresh]);

  const getProfile = async () => {
    if (!Id) return;
    setIsLoading(true);
    try {
      setProfile(null);
      const result = await customerDA.getCustomerbyId(Id);
      if (result.code === 200) {
        const checkFollowCustomer = await customerDA.checkFollowCustomer(Id);
        setCheckFollowCustomer(checkFollowCustomer);
        setProfile({
          ...result.data,
          isFollowing: checkFollowCustomer ? checkFollowCustomer.Status : 0,
        });
        // setAvt(result.data?.AvatarUrl);
      }
    } catch (error) {
      console.error('Failed to fetch group detail:', error);
    } finally {
      setIsLoading(false);
      // Make sure to reset the refresh state
      setTimeout(() => {
        setIsRefresh(false);
      }, 500);
    }
  };
  useEffect(() => {
    if (profile) {
      setAvt(profile?.AvatarUrl);
    }
  }, [profile]);

  const openSheetWithActions = (actions: ActionSheetOption[]) => {
    setCurrentActions(actions);
    // Delay 1 frame để chắc chắn actions được cập nhật trước khi mở
    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,
      title: 'Actions',
      children: (
        <View
          style={{
            height: Dimensions.get('window').height / 5,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <ActionSheet actions={actions} onSelect={handleSelect} />
        </View>
      ),
    });
  };

  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: true,
      cropperCircleOverlay: true,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        await dispatch(
          CustomerActions.edit({
            ...currentUser,
            BackgroundUrl: resImgs[0].Id,
          }),
        ).then((res: any) => {
          if (res.code === 200) {
            setAvt(resImgs[0].Id);
            showSnackbar({
              message: 'Cập nhật ảnh đại diện thành công',
              status: ComponentStatus.SUCCSESS,
              bottom: 60,
            });
            dispatch(CustomerActions.getInfor());
          }
        });
      }
    }
  };

  const handleSelect = (key: string) => {
    console.log('Selected:', key);
    switch (key) {
      // case 'addfriend':
      //   customerDA.follow(profile?.Id).then((res: any) => {
      //     if (res) {
      //       setProfile({...profile, isFollowing: FollowStatus.Pending});
      //     }
      //   });
      //   break;
      case 'addfriend':
        customerDA.Acceptfollow(profile?.Id).then((res: any) => {
          if (res) {
            setProfile({...profile, isFollowing: FollowStatus.Accept});
          }
        });
        break;
      case 'unfriend':
        customerDA.unfollow(profile?.Id).then((res: any) => {
          if (res) {
            setProfile({...profile, isFollowing: FollowStatus.None});
          }
        });
        break;
      case 'cancelfriend':
        customerDA.unfollow(profile?.Id).then((res: any) => {
          if (res) {
            setProfile({...profile, isFollowing: FollowStatus.None});
          }
        });
        break;
      default:
        break;
    }
    hideBottomSheet(bottomSheetRef);
  };
  const headerHeight = 320; // Chiều cao phần header ban đầu
  const tabBarHeight = 50; // Chiều cao của tab bar

  // Animation cho header mới và tabbar
  const newHeaderOpacity = scrollY.interpolate({
    inputRange: [headerHeight - 20, headerHeight],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  // Chuyển đổi giữa các tab
  const changeTab = (tab: any) => {
    setActiveTab(tab);
  };

  const tabs = [
    {Id: 0, Name: 'Posts'},
    // {Id: 1, Name: 'Friends'},
    // {Id: 2, Name: 'About'},
  ];

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {isLoading ? (
        <SkeletonGroupIndex />
      ) : (
        <>
          <FDialog ref={dialogRef} />
          <FBottomSheet ref={bottomSheetRef} />

          {/* Header mới - xuất hiện khi cuộn */}
          <Animated.View
            style={[
              styles.newHeader,
              {
                opacity: newHeaderOpacity,
                // shadowOpacity: fixedElementsShadow,
              },
            ]}
            pointerEvents={isHeaderVisible ? 'auto' : 'none'}>
            <SafeAreaView style={{height: 16}} edges={['top']} />
            <ListTile
              isClickLeading
              style={{borderRadius: 0}}
              leading={
                <TouchableOpacity
                  style={{padding: 4, zIndex: 999}}
                  onPress={() => {
                    navigateBack();
                  }}>
                  <Winicon
                    src="outline/arrows/left-arrow"
                    size={20}
                    color={ColorThemes.light.neutral_text_title_color}
                  />
                </TouchableOpacity>
              }
              title={profile?.Name}
              titleStyle={{
                ...TypoSkin.heading6,
                color: ColorThemes.light.neutral_text_title_color,
              }}
              subtitle={
                profile?.Rank
                  ? `Hạng ${
                      profile?.Rank == CustomerRankType.normal
                        ? 'Thường'
                        : 'VIP'
                    }`
                  : ''
              }
              subTitleStyle={{
                ...TypoSkin.subtitle4,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}
              trailing={
                <TouchableOpacity
                  style={{padding: 4}}
                  onPress={() => {
                    onShare({content: 'hhellloo group'});
                  }}>
                  <Winicon
                    src="fill/arrows/social-sharing"
                    size={20}
                    color={ColorThemes.light.neutral_text_title_color}
                  />
                </TouchableOpacity>
              }
            />
          </Animated.View>

          {/* Content  */}
          <Animated.ScrollView
            bounces={true}
            contentContainerStyle={{paddingTop: headerHeight + tabBarHeight}}
            scrollEventThrottle={16}
            refreshControl={
              <RefreshControl
                refreshing={isRefresh}
                onRefresh={handleMainRefresh}
                colors={[ColorThemes.light.primary_main_color]}
                tintColor={ColorThemes.light.primary_main_color}
              />
            }
            onScroll={Animated.event(
              [{nativeEvent: {contentOffset: {y: scrollY}}}],
              {
                useNativeDriver: true,
                listener: (event: any) => {
                  // Cập nhật trạng thái hiển thị của header dựa trên vị trí cuộn
                  const offsetY = event.nativeEvent.contentOffset.y;
                  setIsHeaderVisible(offsetY > headerHeight - 30);
                },
              },
            )}>
            {/* Header gốc - biến mất khi cuộn */}
            <Animated.View style={[styles.originalHeader]}>
              <ScreenHeader
                style={{
                  backgroundColor: ColorThemes.light.transparent,
                  position: 'absolute',
                  height: 56,
                  zIndex: 111,
                }}
              />
              {/* image */}
              <View style={{height: 248, width: '100%'}}>
                <AppButton
                  prefixIcon={'outline/arrows/left-arrow'}
                  prefixIconSize={20}
                  backgroundColor={
                    ColorThemes.light.neutral_absolute_background_color
                  }
                  textColor={ColorThemes.light.neutral_text_title_color}
                  borderColor="transparent"
                  containerStyle={{
                    position: 'absolute',
                    top: 16,
                    left: 16,
                    zIndex: 111,
                    paddingHorizontal: 12,
                    borderRadius: 100,
                    width: 40,
                    height: 40,
                  }}
                  onPress={navigateBack}
                />

                {profile?.Id === currentUser?.Id ? (
                  <AppButton
                    prefixIcon={'outline/entertainment/camera'}
                    prefixIconSize={16}
                    title={'Edit'}
                    backgroundColor={
                      ColorThemes.light.neutral_absolute_background_color
                    }
                    textColor={ColorThemes.light.neutral_text_title_color}
                    borderColor="transparent"
                    containerStyle={{
                      position: 'absolute',
                      bottom: 8,
                      right: 8,
                      zIndex: 111,
                      paddingHorizontal: 12,
                      borderRadius: 8,
                    }}
                    onPress={pickerImg}
                  />
                ) : null}
                {profile?.BackgroundUrl ? (
                  <FastImage
                    key={profile?.BackgroundUrl}
                    source={
                      profile?.BackgroundUrl
                        ? {
                            uri: `${
                              profile?.BackgroundUrl
                                ? profile?.BackgroundUrl.includes('http')
                                  ? profile?.BackgroundUrl
                                  : ConfigAPI.urlImg + profile?.BackgroundUrl
                                : 'https://placehold.co/48/FFFFFF/000000/png'
                            }`,
                          }
                        : require('../../../assets/images/logo.png')
                    }
                    style={{height: 248, width: '100%'}}
                    resizeMode="contain"
                  />
                ) : (
                  <View
                    style={{
                      height: 248,
                      width: '100%',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.heading1,
                        color:
                          ColorThemes.light.neutral_absolute_background_color,
                      }}>
                      {profile?.Name
                        ? profile.Name.charAt(0).toUpperCase()
                        : ''}
                    </Text>
                  </View>
                )}
              </View>
              <ListTile
                isClickLeading
                leading={
                  profile?.AvatarUrl ? (
                    <ClickableImage
                      key={profile?.AvatarUrl}
                      source={
                        profile?.AvatarUrl
                          ? {
                              uri: `${
                                profile?.AvatarUrl
                                  ? profile?.AvatarUrl.includes('http')
                                    ? profile?.AvatarUrl
                                    : ConfigAPI.urlImg + profile?.AvatarUrl
                                  : 'https://placehold.co/48/FFFFFF/000000/png'
                              }`,
                            }
                          : require('../../../assets/images/logo.png')
                      }
                      style={{
                        height: 56,
                        width: 56,
                        borderRadius: 100,
                        backgroundColor:
                          ColorThemes.light.neutral_main_background_color,
                      }}
                    />
                  ) : (
                    <View
                      style={{
                        height: 56,
                        width: 56,
                        borderRadius: 100,
                        backgroundColor: ColorThemes.light.primary_main_color,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.heading7,
                          color:
                            ColorThemes.light.neutral_absolute_background_color,
                        }}>
                        {profile?.Name
                          ? profile.Name.charAt(0).toUpperCase()
                          : ''}
                      </Text>
                    </View>
                  )
                }
                title={profile?.Name}
                titleStyle={{
                  ...TypoSkin.heading6,
                  color: ColorThemes.light.neutral_text_title_color,
                }}
                subtitle={
                  profile?.Rank && !forEschool
                    ? `Hạng ${
                        profile?.Rank == CustomerRankType.normal
                          ? 'Thường'
                          : 'VIP'
                      }`
                    : ''
                }
                subTitleStyle={{
                  ...TypoSkin.subtitle4,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}
                trailing={
                  <View>
                    {profile?.Id !== currentUser?.Id ? (
                      <AppButton
                        prefixIcon={'outline/user interface/lock'}
                        prefixIconSize={16}
                        title={'Block'}
                        backgroundColor={ColorThemes.light.error_main_color}
                        textColor={
                          ColorThemes.light.neutral_absolute_background_color
                        }
                        borderColor="transparent"
                        containerStyle={{
                          paddingHorizontal: 12,
                          borderRadius: 8,
                        }}
                        onPress={() => {
                          showDialog({
                            ref: dialogRef,
                            status: ComponentStatus.WARNING,
                            title: 'Bạn chắc chắn muốn chặn người này?',
                            content:
                              'Khi bị chặn, người này sẽ không thể gửi tin nhắn, xem nội dung, hoặc tương tác với bạn trong ứng dụng.',
                            onSubmit: async () => {
                              showSnackbar({
                                message: 'Bạn đã chặn người này thành công',
                                status: ComponentStatus.WARNING,
                              });
                              // tìm kiếm id tất cả bài viết của người này đang được hiển thị
                              const postIds = store
                                .getState()
                                .newsFeed.data.filter(
                                  (post: any) => post.CustomerId === profile.Id,
                                )
                                ?.map((post: any) => post.Id);
                              // Ẩn tất cả bài đăng của người này
                              postIds?.forEach((postId: any) => {
                                dispatch(
                                  newsFeedActions.hidePostNocall(postId),
                                );
                              });

                              navigateBack();
                            },
                          });
                        }}
                      />
                    ) : null}
                  </View>
                }
              />
            </Animated.View>
            {/* Tabbar - di chuyển lên trên và được ghim */}
            <Animated.View style={[styles.tabBarContainer]}>
              <View style={styles.tabBar}>
                {forEschool ? (
                  <TouchableOpacity
                    key={0}
                    style={[
                      styles.tabItem,
                      activeTab === 0 && styles.activeTabItem,
                    ]}
                    onPress={() => changeTab(0)}>
                    <Text
                      style={[
                        styles.tabText,
                        activeTab === 0 && styles.activeTabText,
                      ]}>
                      About
                    </Text>
                    {activeTab === 0 && <View style={styles.indicator} />}
                  </TouchableOpacity>
                ) : (
                  tabs.map(item => {
                    return (
                      <TouchableOpacity
                        key={item.Id}
                        style={[
                          styles.tabItem,
                          activeTab === item.Id && styles.activeTabItem,
                        ]}
                        onPress={() => changeTab(item.Id)}>
                        <Text
                          style={[
                            styles.tabText,
                            activeTab === item.Id && styles.activeTabText,
                          ]}>
                          {item.Name}
                        </Text>
                        {activeTab === item.Id && (
                          <View style={styles.indicator} />
                        )}
                      </TouchableOpacity>
                    );
                  })
                )}
              </View>
            </Animated.View>
            {activeTab === 0 && !forEschool ? (
              <PostsTab profile={profile} />
            ) : (
              //  : activeTab === 1 ? (
              //   <Friends profile={profile} scrollEnabled={false} />
              // )
              // <About profile={profile} />
              <View />
            )}
          </Animated.ScrollView>
        </>
      )}
    </SafeAreaView>
  );
}

const PostsTab = ({profile}: {profile: any}) => {
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  const size = 10;
  // const user = useSelectorCustomerState().data;
  const data = useSelector((state: RootState) => state.myFeed.data);
  const {loading} = useSelector((state: RootState) => state.myFeed);
  const page = useSelector((state: RootState) => state.myFeed.page);

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);

  useEffect(() => {
    dispatch(myFeedActions.getMyFeed(1, size, profile?.Id));
  }, []);

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        dispatch(myFeedActions.getMyFeed(1, size, profile?.Id));
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      }
    }
  };

  const handleLoadMore = async () => {
    // Kiểm tra các điều kiện để loadmore
    if (!loading && !isRefreshing && hasMore) {
      try {
        const result = myFeedActions.getMyFeed(page + 1, size, profile?.Id);

        // Kiểm tra kết quả trả về
        if ((result as any)?.payload?.length < size) {
          setHasMore(false); // Nếu số lượng data nhỏ hơn size, đánh dấu là hết data
        } else if ((result as any)?.payload?.length === 0) {
          setHasMore(false); // Nếu không có data trả về, đánh dấu là hết data
        }
      } catch (error) {
        console.error('Load more error:', error);
        setHasMore(false); // Nếu có lỗi, đánh dấu là hết data
      }
    }
  };

  const PostItem = ({
    item,
    user,
    dialogRef,
  }: {
    item: any;
    user: any;
    dialogRef: any;
  }) => {
    const dispatch: AppDispatch = useDispatch();

    const actionView = (
      <View
        style={{
          flexDirection: 'row',
          paddingTop: 16,
          alignItems: 'center',
          gap: 8,
        }}>
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            padding: 4,
            height: 24,
            paddingVertical: 0,
            paddingHorizontal: 8,
          }}
          onPress={async () => {
            if (user) {
              dispatch(myFeedActions.updateLike(item.Id, !item.IsLike));
              const newfeed = store
                .getState()
                .newsFeed.data.find((item: any) => item.Id === item.Id);
              if (newfeed) {
                dispatch(newsFeedActions.updateLike(item.Id, !item.IsLike));
              }
            } else {
              ///TODO: check chưa login thì confirm ra trang login
              dialogCheckAcc(dialogRef);
            }
          }}
          title={
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              {item.Likes ?? 0}
            </Text>
          }
          textColor={
            item.IsLike === true
              ? ColorThemes.light.error_main_color
              : ColorThemes.light.neutral_text_subtitle_color
          }
          prefixIconSize={12}
          prefixIcon={
            item.IsLike === true
              ? 'fill/emoticons/heart'
              : 'outline/emoticons/heart'
          }
        />
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            padding: 4,
            height: 24,
            paddingVertical: 0,
            paddingHorizontal: 8,
          }}
          onPress={async () => {
            navigate(RootScreen.PostDetail, {item: item});
          }}
          prefixIcon={'outline/user interface/b-comment'}
          prefixIconSize={12}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
          title={
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.neutral_text_subtitle_color,
              }}>
              {item.Comment ?? 0}
            </Text>
          }
        />
        {/* <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          containerStyle={{
            padding: 4,
            height: 24,
            paddingVertical: 0,
            paddingHorizontal: 8,
          }}
          onPress={async () => {
            onShare({content: 'Hello world'});
          }}
          prefixIcon={'fill/arrows/social-sharing'}
          prefixIconSize={12}
          textColor={ColorThemes.light.neutral_text_subtitle_color}
        /> */}
      </View>
    );

    const trailingView = (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 4,
        }}>
        <AppButton
          backgroundColor={ColorThemes.light.neutral_main_background_color}
          borderColor="transparent"
          onPress={() => {
            showBottomSheet({
              ref: bottomSheetRef,
              enableDismiss: true,
              title: 'Actions',
              suffixAction: <View />,
              prefixAction: (
                <TouchableOpacity
                  onPress={() => hideBottomSheet(bottomSheetRef)}
                  style={{padding: 6, alignItems: 'center'}}>
                  <Winicon
                    src="outline/layout/xmark"
                    size={20}
                    color={ColorThemes.light.Neutral_Text_Color_Body}
                  />
                </TouchableOpacity>
              ),
              children: (
                <View
                  style={{
                    // gap: 4,
                    height: Dimensions.get('window').height / 4.5,
                    width: '100%',
                    backgroundColor:
                      ColorThemes.light.neutral_absolute_background_color,
                  }}>
                  {profile.Id === customer.Id && (
                    <ListTile
                      onPress={() => {
                        hideBottomSheet(bottomSheetRef);
                        navigation.push(RootScreen.createPost, {
                          editPost: item,
                          groupId: item.GroupId,
                        });
                      }}
                      title={'Edit post'}
                      titleStyle={{...TypoSkin.body3}}
                    />
                  )}
                  {profile.Id === customer.Id && (
                    <ListTile
                      onPress={() => {
                        hideBottomSheet(bottomSheetRef);
                        dispatch(myFeedActions.deletePost(item));
                        const newfeed = store
                          .getState()
                          .newsFeed.data.find((a: any) => a.Id === item.Id);
                        if (newfeed) {
                          dispatch(newsFeedActions.hidePostNocall(newfeed.Id));
                        }
                      }}
                      title={'Delete post'}
                      titleStyle={{...TypoSkin.body3}}
                    />
                  )}
                </View>
              ),
            });
          }}
          containerStyle={{
            borderRadius: 100,
            padding: 6,
            height: 24,
            width: 24,
          }}
          title={
            <Winicon
              src={'fill/user interface/menu-dots'}
              size={14}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
          }
        />
      </View>
    );

    return (
      <DefaultPost
        data={{
          ...item,
          relativeUser: {
            image: user?.AvatarUrl,
            title: user?.Name,
            subtitle: Ultis.getDiffrentTime(item.DateCreated),
          },
        }}
        containerStyle={{paddingHorizontal: 16}}
        onPressDetail={() =>
          navigate(RootScreen.PostDetail, {
            item: {
              ...item,
              relativeUser: {
                image: user?.AvatarUrl,
                title: user?.Name,
                subtitle: Ultis.getDiffrentTime(item.DateCreated),
              },
            },
          })
        }
        actionView={actionView}
        trailingView={profile?.Id === customer.Id ? trailingView : null}
        showContent={true}
      />
    );
  };

  return (
    <View style={{flex: 1}}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      {profile?.Id === customer?.Id && (
        <View
          style={{
            borderRadius: 8,
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
            marginTop: 8,
            gap: 8,
            padding: 16,
            alignContent: 'center',
            justifyContent: 'center',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 16,
              width: '100%',
            }}>
            <TouchableOpacity
              style={{
                flex: 1,
                height: 40,
                justifyContent: 'center',
                backgroundColor:
                  ColorThemes.light.neutral_main_background_color,
                borderRadius: 8,
              }}
              onPress={() => {
                navigation.push(RootScreen.createPost, {groupId: null});
              }}>
              <Text
                style={{
                  ...TypoSkin.placeholder1,
                  color: ColorThemes.light.Neutral_Text_Color_Placeholder,
                  paddingHorizontal: 12,
                }}>
                Bạn đang nghĩ gì ?
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
      <FlatList
        scrollEnabled={false}
        data={data}
        renderItem={({item}) => (
          <PostItem item={item} user={profile} dialogRef={dialogRef} />
        )}
        keyExtractor={item => item.Id.toString()}
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        contentContainerStyle={{
          gap:
            data?.filter((item: any) => item.CustomerId === profile?.Id)
              .length > 0
              ? 8
              : 0,
          backgroundColor: ColorThemes.light.neutral_main_background_color,
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading) {
            return (
              <View style={{gap: 8}}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlaceCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          }
          return (
            <View
              style={{
                flex: 1,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
              }}>
              <EmptyPage title="Không có dữ liệu" />
            </View>
          );
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return <SkeletonPlaceCard />;
          }
          if (!hasMore && data.length > 0) {
            return (
              <View
                style={{
                  padding: 16,
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: ColorThemes.light.neutral_text_subtitle_color,
                    ...TypoSkin.subtitle2,
                  }}>
                  Không còn dữ liệu
                </Text>
              </View>
            );
          }
          return null;
        }}
      />
      <View style={{height: 60}} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  originalHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 320,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    zIndex: 1,
  },
  originalHeaderText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  newHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },
  newHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tabBarContainer: {
    position: 'absolute',
    top: 320, // Bắt đầu dưới header gốc
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: '#fff',
    zIndex: 2,
    borderBottomWidth: 0.5,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: '100%',
    alignItems: 'center',
  },
  tabItem: {
    paddingVertical: 15,
    marginRight: 24,
    position: 'relative',
  },
  activeTabItem: {
    // Style cho tab active
  },
  tabText: {
    ...TypoSkin.label4,
  },
  activeTabText: {
    color: ColorThemes.light.primary_main_color,
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1.5,
    backgroundColor: ColorThemes.light.primary_main_color,
  },
});

// Component SkeletonGroupIndex để hiển thị trong trạng thái loading
function SkeletonGroupIndex() {
  return (
    <View style={{flex: 1, backgroundColor: '#fff'}}>
      {/* Skeleton cho header */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={{height: 320}}>
          {/* Header với nút back */}
          <View
            style={{
              height: 56,
              paddingHorizontal: 16,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <View style={{width: 32, height: 32, borderRadius: 16}} />
          </View>

          {/* Ảnh cover */}
          <View style={{height: 248, width: '100%'}} />

          {/* Thông tin nhóm */}
          <View style={{padding: 16}}>
            <View
              style={{width: 200, height: 24, borderRadius: 4, marginBottom: 8}}
            />
            <View
              style={{
                width: 120,
                height: 16,
                borderRadius: 4,
                marginBottom: 16,
              }}
            />

            {/* Avatar members */}
            <View style={{flexDirection: 'row'}}>
              {[1, 2, 3].map((_, index) => (
                <View
                  key={index}
                  style={{
                    width: 24,
                    height: 24,
                    borderRadius: 12,
                    marginLeft: index > 0 ? -10 : 0,
                  }}
                />
              ))}
            </View>

            {/* Buttons */}
            <View style={{flexDirection: 'row', gap: 8, marginTop: 16}}>
              <View style={{flex: 1, height: 40, borderRadius: 8}} />
              <View style={{flex: 1, height: 40, borderRadius: 8}} />
            </View>
          </View>
        </View>
      </SkeletonPlaceholder>

      {/* Skeleton cho tab bar */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={{height: 50, flexDirection: 'row', paddingHorizontal: 20}}>
          <View
            style={{width: 100, height: 20, borderRadius: 4, marginRight: 24}}
          />
          <View style={{width: 100, height: 20, borderRadius: 4}} />
        </View>
      </SkeletonPlaceholder>

      {/* Skeleton cho nội dung */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={{padding: 16, gap: 16}}>
          {/* Post input */}
          <View
            style={{
              height: 69,
              padding: 16,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 16,
            }}>
            <View style={{width: 32, height: 32, borderRadius: 16}} />
            <View style={{flex: 1, height: 40, borderRadius: 8}} />
          </View>

          {/* Posts */}
          {[1, 2, 3].map((_, index) => (
            <View key={index} style={{padding: 16, borderRadius: 8, gap: 12}}>
              {/* Header with avatar and name */}
              <SkeletonPlaceCard />
            </View>
          ))}
        </View>
      </SkeletonPlaceholder>
    </View>
  );
}

// trailing={
//   profile?.Id === currentUser?.Id ? (
//     <View />
//   ) : profile?.isFollowing === 0 ? (
//     <AppButton
//       prefixIcon={'fill/users/user-new'}
//       prefixIconSize={16}
//       title={'Thêm bạn'}
//       backgroundColor={ColorThemes.light.primary_main_color}
//       textColor={
//         ColorThemes.light.neutral_absolute_background_color
//       }
//       borderColor="transparent"
//       containerStyle={{
//         height: 36,
//         paddingHorizontal: 8,
//         borderRadius: 8,
//       }}
//       onPress={async () => {
//         const customerDa = new CustomerDA();
//         const result = await customerDa.follow(profile?.Id);
//         if (result) {
//           setProfile({
//             ...profile,
//             isFollowing: FollowStatus.Pending,
//           });
//           setCheckFollowCustomer(result);
//         }
//       }}
//     />
//   ) : profile?.isFollowing === FollowStatus.Accept ? (
//     <AppButton
//       prefixIcon={'fill/users/user-check'}
//       prefixIconSize={16}
//       title={'Bạn bè'}
//       backgroundColor={ColorThemes.light.primary_main_color}
//       textColor={
//         ColorThemes.light.neutral_absolute_background_color
//       }
//       borderColor="transparent"
//       containerStyle={{
//         height: 36,
//         paddingHorizontal: 8,
//         borderRadius: 8,
//       }}
//       onPress={async () => {
//         openSheetWithActions([
//           // {
//           //   key: 'accept',
//           //   label: 'Chấp nhận',
//           //   icon: (
//           //     <Winicon
//           //       src="fill/users/user-new"
//           //       size={16}
//           //       color={ColorThemes.light.primary_main_color}
//           //     />
//           //   ),
//           //   destructive: false,
//           // },
//           {
//             key: 'unfriend',
//             label: 'Hủy kết bạn',
//             icon: (
//               <Winicon
//                 src="outline/layout/xmark"
//                 size={16}
//                 color={ColorThemes.light.error_main_color}
//               />
//             ),
//           },
//         ]);
//       }}
//     />
//   ) : currentUser.Id === checkFollowCustomer?.CustomerId ? (
//     <AppButton
//       prefixIcon={'fill/users/user-delete-cross'}
//       prefixIconSize={16}
//       title={'Hủy lời mời'}
//       backgroundColor={
//         ColorThemes.light.neutral_main_background_color
//       }
//       textColor={
//         ColorThemes.light.Neutral_Text_Color_Placeholder
//       }
//       borderColor="transparent"
//       containerStyle={{
//         height: 36,
//         paddingHorizontal: 8,
//         borderRadius: 8,
//       }}
//       onPress={async () => {
//         const result = await customerDA.unfollow(profile?.Id);
//         if (result) {
//           setProfile({...profile, isFollowing: 0});
//         }
//       }}
//     />
//   ) : (
//     <AppButton
//       prefixIcon={'fill/users/user-check'}
//       prefixIconSize={16}
//       title={'Phản hồi'}
//       backgroundColor={ColorThemes.light.Success_Color_Main}
//       textColor={ColorThemes.light.white}
//       borderColor="transparent"
//       containerStyle={{
//         height: 36,
//         paddingHorizontal: 8,
//         borderRadius: 8,
//       }}
//       onPress={async () => {
//         openSheetWithActions([
//           {
//             key: 'addfriend',
//             label: 'Chấp nhận',
//             icon: (
//               <Winicon
//                 src="fill/users/user-new"
//                 size={16}
//                 color={ColorThemes.light.primary_main_color}
//               />
//             ),
//           },
//           {
//             key: 'cancelfriend',
//             label: 'Từ chối',
//             icon: (
//               <Winicon
//                 src="outline/layout/xmark"
//                 size={16}
//                 color={ColorThemes.light.error_main_color}
//               />
//             ),
//           },
//         ]);
//         const result = await customerDA.unfollow(profile?.Id);
//         if (result) {
//           setProfile({...profile, isFollowing: false});
//         }
//       }}
//     />
//   )
// }
