import React, {useState, useEffect} from 'react';
import {useForm} from 'react-hook-form';
import {useTranslation} from 'react-i18next';
import {
  Pressable,
  KeyboardAvoidingView,
  View,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {validatePhoneNumber} from '../../../utils/validate';
import {FAddressPickerForm} from '../../Default/form/component-form';
import {TextFieldForm} from '../../news/form/component-form';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton, ListTile, Winicon} from 'wini-mobile-components';
import IOSSwitch from '../../../components/IOSSwitch';
import WScreenFooter from '../../../Screen/Layout/footer';
import {navigateBack, RootScreen} from '../../../router/router';
import {useDispatch} from 'react-redux';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {randomGID} from '../../../utils/Utils';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {useRoute} from '@react-navigation/native';

export default function EditAddress() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const item = route?.params?.item;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID(), DateCreated: new Date().getTime()},
  });

  const [isDefault, setIsDefault] = useState(false);

  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;
  const addresses = useSelectorCustomerState().myAddress;

  useEffect(() => {
    if (item) {
      Object.keys(item).forEach(key => {
        methods.setValue(key, item[key]);
      });
      setIsDefault(item.IsDefault);
    }
  }, [item]);

  useEffect(() => {
    if (addresses && addresses.length == 0) {
      setIsDefault(true);
      // add methods value by customer
      methods.setValue('Name', customer.Name);
      methods.setValue('Mobile', customer.Mobile);
      methods.setValue('Email', customer.Email);
      methods.setValue('Address', customer.Address);
      methods.setValue('Long', customer.Long);
      methods.setValue('Lat', customer.Lat);
      methods.setValue('CustomerId', customer.Id);
    }
  }, [addresses]);

  const onSubmit = () => {
    const data = methods.getValues();
    const obj = {
      ...data,
      CustomerId: customer.Id,
      IsDefault: isDefault,
    };
    if (item) {
      data.Id = item.Id;
      data.DateCreated = item.DateCreated;
    }
    dispatch(CustomerActions.editAddress(obj, item ? false : true));
    navigateBack();
  };

  return (
    <Pressable
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <InforHeader title="Chi tiết địa chỉ" />
      <TouchableWithoutFeedback
        style={{flex: 1}}
        onPress={() => Keyboard.dismiss()}>
        <KeyboardAvoidingView>
          <View
            style={{
              paddingHorizontal: 16,
              gap: 16,
              paddingVertical: 8,
              paddingBottom: 100,
            }}>
            <TextFieldForm
              control={methods.control}
              name="Name"
              required
              placeholder={t('profile.name')}
              label={t('profile.name')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="name-phone-pad"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Name');
                else
                  methods.setError('Name', {
                    message: 'Họ và tên không được để trống',
                  });
              }}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Email"
              placeholder={t('profile.email')}
              label={t('profile.email')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="email-address"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Email');
                else
                  methods.setError('Email', {
                    message: 'Email không được để trống',
                  });
              }}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              required
              placeholder={t('profile.phone')}
              label={t('profile.phone')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/user interface/phone"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              type="number-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
                  return;
                }
                var mobile = ev.trim();
                // Check if the number doesn't already start with 0 or +84
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile; // Add 0 at the beginning
                }
                const val = validatePhoneNumber(mobile);
                if (val) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
              }}
            />
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              label={t('profile.address')}
              placeholder={t('profile.address')}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/location/map-marker"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
              textFieldStyle={{
                paddingLeft: 8,
                gap: 12,
              }}
            />
            <ListTile
              title="Đặt làm địa chỉ mặc định"
              style={{padding: 0, marginTop: 16}}
              titleStyle={[
                TypoSkin.heading8,
                {
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                },
              ]}
              trailing={
                <IOSSwitch
                  disabled={addresses && addresses.length == 0}
                  value={isDefault}
                  onColor={ColorThemes.light.primary_main_color}
                  onValueChange={setIsDefault}
                />
              }
            />
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
      <WScreenFooter>
        <AppButton
          containerStyle={{
            borderRadius: 8,
            marginHorizontal: 16,
            flex: 1,
            backgroundColor: ColorThemes.light.primary_main_color,
            justifyContent: 'center',
          }}
          borderColor="transparent"
          title={'Lưu'}
          onPress={() => {
            methods.handleSubmit(onSubmit)();
          }}
        />
      </WScreenFooter>
    </Pressable>
  );
}
