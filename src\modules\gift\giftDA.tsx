import {DataController} from '../../base/baseController';
import {
  StorageContanst,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {randomGID, Ultis} from '../../utils/Utils';

export interface GiftItem {
  Id?: string;
  Name?: string;
  Description?: string;
  Img?: string;
  Value: number;
  Status?: number;
  DateCreated?: string;
  ExpriseDate?: number;
  Quantity?: number;
  Category?: string;
}

export interface ExchangeGiftItem {
  Id?: string;
  GiftId?: string;
  CustomerId?: string;
  Value: number;
  Status: number; // 0: <PERSON><PERSON> chờ duy<PERSON>t, 1: Đ<PERSON> du<PERSON>, 2: Từ chối
  DateCreated?: string;
  DateApproved?: string;
  Note?: string;
  Gift?: GiftItem;
}

export class GiftDA {
  private giftController: DataController;
  private exchangeGiftController: DataController;
  private historyRewardController: DataController;

  constructor() {
    this.giftController = new DataController('Gifts');
    this.exchangeGiftController = new DataController('ExchangeGifts');
    this.historyRewardController = new DataController('HistoryReward');
  }

  // Lấy danh sách quà tặng
  async getGifts(page: number = 1, size: number = 10, category?: string) {
    let query = '@Status: [1]'; // Chỉ lấy quà tặng đang hoạt động

    if (category && category !== 'all') {
      query += ` @Category: {${category}}`;
    }

    const response = await this.giftController.getListSimple({
      page,
      size,
      query,
      returns: [
        'Id',
        'Name',
        'Description',
        'Img',
        'Value',
        'ExpriseDate',
        'Quantity',
        'Category',
      ],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });

    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  // Lấy chi tiết quà tặng
  async getGiftDetail(giftId: string) {
    const response = await this.giftController.getListSimple({
      query: `@Id: {${giftId}}`,
      returns: [
        'Id',
        'Name',
        'Description',
        'Img',
        'Value',
        'ExpriseDate',
        'Quantity',
        'Category',
      ],
    });

    if (response?.code === 200 && response.data?.length > 0) {
      return response.data[0];
    }
    return null;
  }

  // Lấy số điểm hiện tại của customer
  async getCurrentPoints(customerId: string) {
    const response = await this.historyRewardController.getListSimple({
      //nếu trạng thái + tiền mà thành công thì mới + vào ví. Còn trạng thái pending thì không cộng vào ví. với giao dịch - tiền thì tính cả
      query: `@CustomerId: {${customerId}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
      returns: ['Value'],
    });

    if (response?.code === 200) {
      const totalPoints = response.data.reduce(
        (total: number, item: any) => total + parseFloat(item.Value),
        0,
      );
      return totalPoints;
    }
    return 0;
  }

  // Đổi quà
  async exchangeGift(
    giftId: string,
    customerId: string,
    points: number,
    giftName: string,
  ) {
    const exchangeData = {
      Id: randomGID(),
      Name: 'Đổi quà',
      GiftsId: giftId,
      CustomerId: customerId,
      Value: parseFloat(points.toString()),
      Status: 0, // Đang chờ duyệt
      DateCreated: new Date().getTime(),
      Quantity: 1,
    };

    const response = await this.exchangeGiftController.add([exchangeData]);

    if (response?.code === 200) {
      //tạo bản ghi trừ điểm
      const historyData = {
        Id: randomGID(),
        CustomerId: customerId,
        Value: -parseFloat(points.toString()),
        Description: 'Đổi quà' + ': ' + giftName,
        Type: TransactionType.Gift,
        DateCreated: new Date().getTime(),
        Code: Ultis.randomString(12),
        Status: TransactionStatus.success,
      };
      await this.historyRewardController.add([historyData]);
      return response;
    }
    return null;
  }

  // Lấy lịch sử đổi quà
  async getExchangeHistory(
    customerId: string,
    page: number = 1,
    size: number = 10,
    status?: number,
  ) {
    let query = `@CustomerId: {${customerId}}`;

    if (status !== undefined) {
      query += ` @Status: [${status}]`;
    }

    const response = await this.exchangeGiftController.getPatternList({
      page,
      size,
      query,
      pattern: {
        GiftsId: ['Id', 'Name', 'Img', 'Value'],
      },
    });

    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  // Lấy danh sách categories của quà tặng
  async getGiftCategories() {
    const response = await this.giftController.group({
      reducers: 'LOAD * GROUPBY 1 @Category REDUCE COUNT 0 AS Count',
      searchRaw: '@Status: [1]',
    });

    if (response?.code === 200) {
      return response.data.map((item: any) => ({
        id: item.Category,
        name: item.Category,
        count: item.Count,
      }));
    }
    return [];
  }
}

export default GiftDA;
