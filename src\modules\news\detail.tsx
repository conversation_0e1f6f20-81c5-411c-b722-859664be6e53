import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Text,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppButton, AppSvg, FLoading} from 'wini-mobile-components';

import iconSvg from '../../svg/icon';
import {ColorThemes} from '../../assets/skin/colors';
import {NewsItem} from '../../redux/models/news';
import ActionBar from './DetailNewsComponent/ActionBar';
import BasicInfoNews from './DetailNewsComponent/BasicInfoNews';
import Content from './DetailNewsComponent/Content';
import ListHastTag from './DetailNewsComponent/ListHastTag';

import {newsAction} from '../../redux/actions/newsAction';
import {navigate, RootScreen} from '../../router/router';
import CommentsListNews from '../customer/listview/commentsNews';
import {useForm} from 'react-hook-form';
import {TypoSkin} from '../../assets/skin/typography';
import {TextFieldForm} from './form/component-form';
import {AppDispatch} from '../../redux/store/store';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {
  fetchComments,
  newsFeedActions,
} from '../community/reducers/newsFeedReducer';
import {postCommentsActions} from '../community/reducers/postCommentsReducer';
import {useDispatch} from 'react-redux';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {DataController} from '../../base/baseController';

const DetailNews = () => {
  const route = useRoute();
  const {id} = route.params as {id: string};
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [news, setNews] = useState<NewsItem | null>(null);

  const methods = useForm<any>({shouldFocusError: false});
  const dispatch: AppDispatch = useDispatch();
  const user = useSelectorCustomerState().data;
  const scrollViewRef = useRef<ScrollView>(null);
  const dialogRef = useRef<any>(null);

  useEffect(() => {
    initData();
  }, []);

  const initData = async () => {
    try {
      setLoading(true);
      const res = await newsAction.fetchById(id);
      if (res) {
        // Fetch comments and likes data in parallel
        const [commentsData, likesData] = await Promise.all([
          fetchCommentsForNews(res.Id),
          fetchLikesForNews(res.Id),
        ]);

        setNews({
          ...res,
          Comment: commentsData.commentsCount,
          Likes: likesData.likesCount,
          LikesCount: likesData.likesCount,
          IsLike: likesData.isLiked,
          customerLiked: likesData.customerLiked,
        });
      }
    } catch (error) {
      console.error('Error loading news data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Separate function to fetch comments for news
  const fetchCommentsForNews = async (newsId: string) => {
    try {
      const commentsResult = await fetchComments([newsId], true);
      const commentsCount =
        commentsResult?.length > 0 ? commentsResult[0]?.CommentsCount || 0 : 0;
      return {commentsCount};
    } catch (error) {
      console.error('Error fetching comments:', error);
      return {commentsCount: 0};
    }
  };

  // Separate function to fetch likes for news with customer information
  const fetchLikesForNews = async (newsId: string) => {
    try {
      // Get likes with customer information
      const likeController = new DataController('Like');
      const response = await likeController.getPatternList({
        page: 1,
        size: 10000,
        query: `@NewsId:{${newsId}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
        },
      });

      if (response.code === 200) {
        const customers = response.Customer || [];
        const likesCount = customers.length;
        const isLiked = user
          ? customers.some((customer: any) => customer.Id === user.Id)
          : false;

        return {
          likesCount,
          isLiked,
          customerLiked: customers,
        };
      }

      return {likesCount: 0, isLiked: false, customerLiked: []};
    } catch (error) {
      console.error('Error fetching likes:', error);
      return {likesCount: 0, isLiked: false, customerLiked: []};
    }
  };

  const navigateToHashtagScreen = (hashtag: string) => {
    navigate(RootScreen.ListByHashtagScreen, {
      hashtag,
      type: 'news',
    });
  };

  const handleToggleLike = useCallback(
    async (isCurrentlyLiked: boolean) => {
      if (!user || !news?.Id) {
        return;
      }

      const newLikeState = !isCurrentlyLiked;

      // Optimistic update - preserve customerLiked and update it
      setNews((prevData: any) => {
        // Make sure we have a valid array to work with
        let updatedCustomerLiked = Array.isArray(prevData.customerLiked)
          ? [...prevData.customerLiked]
          : [];

        if (newLikeState) {
          // Add current user to customerLiked if not already there
          const userExists = updatedCustomerLiked.some(
            customer => customer.Id === user.Id,
          );
          if (!userExists) {
            updatedCustomerLiked.push({
              Id: user.Id,
              Name: user.Name,
              AvatarUrl: user.AvatarUrl,
            });
          }
        } else {
          // Remove current user from customerLiked
          updatedCustomerLiked = updatedCustomerLiked.filter(
            customer => customer.Id !== user.Id,
          );
        }

        return {
          ...prevData,
          IsLike: newLikeState,
          Likes:
            newLikeState === false
              ? Math.max(0, (prevData.Likes || 0) - 1)
              : (prevData.Likes || 0) + 1,
          LikesCount:
            newLikeState === false
              ? Math.max(0, (prevData.LikesCount || 0) - 1)
              : (prevData.LikesCount || 0) + 1,
          customerLiked: updatedCustomerLiked,
        };
      });

      try {
        // Update server - use news.Id for consistency
        await dispatch(
          newsFeedActions.updateLike(news.Id, isCurrentlyLiked, true),
        );
      } catch (error) {
        console.error('Failed to update like:', error);
        // Revert optimistic update on error
        setNews((prevData: any) => {
          let revertedCustomerLiked = [...(prevData.customerLiked || [])];

          if (isCurrentlyLiked) {
            // Re-add current user to customerLiked
            const userExists = revertedCustomerLiked.some(
              customer => customer.Id === user.Id,
            );
            if (!userExists) {
              revertedCustomerLiked.push({
                Id: user.Id,
                Name: user.Name,
                AvatarUrl: user.AvatarUrl,
              });
            }
          } else {
            // Remove current user from customerLiked
            revertedCustomerLiked = revertedCustomerLiked.filter(
              customer => customer.Id !== user.Id,
            );
          }

          return {
            ...prevData,
            IsLike: isCurrentlyLiked,
            Likes: isCurrentlyLiked
              ? (prevData.Likes || 0) + 1
              : Math.max(0, (prevData.Likes || 0) - 1),
            LikesCount: isCurrentlyLiked
              ? (prevData.LikesCount || 0) + 1
              : Math.max(0, (prevData.LikesCount || 0) - 1),
            customerLiked: revertedCustomerLiked,
          };
        });
      }
    },
    [dispatch, news?.Id, user],
  );

  const handleReply = useCallback(
    async (dataComment?: any) => {
      if (dataComment) {
        methods.setValue('Comment', undefined);
        methods.setValue('Comment', `@${dataComment.relativeUser.title} `);
        methods.setValue('CommentId', `${dataComment.Id}`);
        methods.setValue('UserComment', `${dataComment.relativeUser.title}`);
        // Focus the comment input field
      } else {
        methods.setValue('Comment', undefined);
        methods.setValue('CommentId', undefined);
        methods.setValue('UserComment', undefined);
      }
    },
    [methods],
  );

  const handleAddComment = async () => {
    if (user) {
      if (methods.getValues().Comment) {
        if (news)
          await dispatch(
            postCommentsActions.addNewComment(
              news?.Id,
              methods.getValues().Comment,
              methods.getValues().CommentId?.toString().trim() || undefined,
              true,
            ),
          );

        setNews((prevData: any) => {
          return {
            ...prevData,
            Comment: prevData.Comment + 1,
          };
        });

        if (
          methods.getValues().CommentId === undefined ||
          methods.getValues().CommentId === ''
        ) {
          scrollViewRef.current?.scrollToEnd({
            animated: true,
          });
        }

        methods.setValue('Comment', '');
        methods.setValue('CommentId', '');
        methods.setValue('UserComment', '');

        if (news) dispatch(postCommentsActions.loadComments(news?.Id, true));
      }
    } else {
      ///TODO: check chưa login thì confirm ra trang login
      dialogCheckAcc(dialogRef);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      {/* Button back luôn hiển thị ở góc */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}>
        <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
      </TouchableOpacity>

      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        {/* Main Image */}
        <View>
          <Image source={{uri: news?.Img}} style={styles.mainImage} />
        </View>

        <View style={{marginHorizontal: 16}}>
          {/* các thao tác */}
          {news && (
            <ActionBar handleToggleLike={handleToggleLike} data={news} />
          )}

          {/* thông tin cơ bản */}
          {news && <BasicInfoNews postData={news} />}

          {/* nội dung */}
          {news && <Content data={news?.Content} />}

          {/* hashtag */}
          {news && (
            <ListHastTag
              hashtags={news?.Hashtag || ''}
              onPress={navigateToHashtagScreen}
            />
          )}

          {/* bình luận */}
          {/* {news && <ListComment />} */}
          {news && (
            <CommentsListNews
              postId={news?.Id}
              isNew={true}
              onReply={handleReply}
            />
          )}
        </View>
      </ScrollView>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}>
        <View
          style={{
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
            paddingHorizontal: 16,
            marginBottom: 32,
            paddingTop: 8,
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
          }}>
          {methods.watch('CommentId') ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                Trả lời {methods.getValues().UserComment ?? ''}
              </Text>
              <Text
                onPress={() => {
                  methods.setValue('CommentId', undefined);
                  methods.setValue('Comment', undefined);
                  methods.setValue('UserComment', undefined);
                }}
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_text_subtitle_color,
                }}>
                - Hủy
              </Text>
            </View>
          ) : null}
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TextFieldForm
              textFieldStyle={{
                padding: 16,
                height: 40,
                paddingVertical: 0,
              }}
              style={{
                flex: 1,
              }}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              placeholder="Viết bình luận của bạn"
              name="Comment"
            />
            <AppButton
              prefixIcon={'fill/user interface/send-message'}
              prefixIconSize={24}
              backgroundColor={ColorThemes.light.transparent}
              borderColor="transparent"
              containerStyle={{
                paddingHorizontal: 12,
                height: 45,
              }}
              onPress={handleAddComment}
              textColor={ColorThemes.light.primary_main_color}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  mainImage: {
    width: '100%',
    height: 400,
    resizeMode: 'cover',
  },
  backButton: {
    position: 'absolute',
    top: 65,
    left: 16,
    backgroundColor: ColorThemes.light.secondary1_sub_color,
    width: 35,
    height: 35,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    elevation: 5, // cho Android
  },
});

export default DetailNews;
