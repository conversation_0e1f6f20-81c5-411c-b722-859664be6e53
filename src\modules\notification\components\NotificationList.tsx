import React, {useMemo, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import {format, isToday, isYesterday, parseISO} from 'date-fns';
import {vi} from 'date-fns/locale';
import {NotificationItem} from '../../../redux/models/notification';
import NotificationCard from './card/NotificationCard';
import {ColorThemes} from '../../../assets/skin/colors';
import {useSelector, useDispatch} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {NotificationActions} from '../../../redux/reducers/notificationReducer';

const NotificationList = () => {
  const dispatch = useDispatch();
  const {loading, data, onLoading} = useSelector(
    (state: RootState) => state.notification,
  );
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await NotificationActions.getData(dispatch)();
    } catch (error) {
      console.error('Error refreshing notifications:', error);
    } finally {
      setRefreshing(false);
    }
  }, [dispatch]);

  const sections = useMemo(() => {
    const grouped = data.reduce((acc: any, notification: NotificationItem) => {
      const date = new Date(notification.DateCreated);
      let title = '';

      if (isToday(date)) {
        title = 'Hôm nay';
      } else if (isYesterday(date)) {
        title = 'Hôm qua';
      } else {
        title = format(date, 'dd/MM/yyyy', {locale: vi});
      }

      if (!acc[title]) {
        acc[title] = [];
      }
      acc[title].push(notification);
      return acc;
    }, {});

    return Object.keys(grouped).map(title => ({
      title: title,
      data: grouped[title],
    }));
  }, [data]);

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.primary_main_color}
        />
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Hiện tại chưa có thông báo.</Text>
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.listContainer}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[ColorThemes.light.primary_main_color]}
          tintColor={ColorThemes.light.primary_main_color}
        />
      }>
      {sections.map(section => (
        <View key={section.title}>
          <Text style={styles.sectionHeader}>{section.title}</Text>
          {section.data.map((item: NotificationItem, itemIndex: number) => (
            <View
              key={item.Id}
              style={{
                backgroundColor: ColorThemes.light.primary_background,
                borderRadius: 16,
              }}>
              <NotificationCard
                item={item}
                showDivider={itemIndex < section.data.length - 1}
              />
            </View>
          ))}
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: 16,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '500',
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 16,
    marginVertical: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#8A8A8E',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NotificationList;
