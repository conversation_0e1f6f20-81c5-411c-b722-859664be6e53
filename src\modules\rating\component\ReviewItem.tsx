import React from 'react';
import { View, Image, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { AppSvg, Winicon } from 'wini-mobile-components';
import { TypeMenuReview } from '../../../Config/Contanst';
import iconSvg from '../../../svg/icon';
import { TypoSkin } from '../../../assets/skin/typography';
import ReviewProduct from '../list/ReviewProduct';

const ReviewItem = () => {
    const [selected, setSelected] = React.useState<string>(TypeMenuReview.Product);
    const handleSelectMenu = (type: string) => {
        setSelected(type);
    };
    return (
        <View style={styles.container}>
            {/* Header với các nút */}
            <View style={styles.header}>
                <TouchableOpacity style={selected == TypeMenuReview.Product ? styles.buttonAction : styles.button} onPress={() => handleSelectMenu(TypeMenuReview.Product)}>
                    {selected == TypeMenuReview.Product ? <AppSvg SvgSrc={iconSvg.productReviewAction} size={19} /> : <AppSvg SvgSrc={iconSvg.productReview} size={19} />}
                    <Text style={selected == TypeMenuReview.Product ? styles.buttonTextAction : styles.buttonText}>Sản phẩm</Text>
                </TouchableOpacity>
                <TouchableOpacity style={selected == TypeMenuReview.Order ? styles.buttonAction : styles.button} onPress={() => handleSelectMenu(TypeMenuReview.Order)}>
                    {selected == TypeMenuReview.Order ? <AppSvg SvgSrc={iconSvg.orderReviewAction} size={19} /> : <AppSvg SvgSrc={iconSvg.orderReview} size={19} />}
                    <Text style={selected == TypeMenuReview.Order ? styles.buttonTextAction : styles.buttonText}>Đơn hàng</Text>
                </TouchableOpacity>
            </View>
            <ReviewProduct
                type={selected}
            />
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 5,
        margin: 10,
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        padding: 10,
    },
    button: {
        padding: 5,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 10,
        marginLeft: 16,
    },
    buttonAction: {
        padding: 5,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: "#33CCFF",
        borderRadius: 10,
        marginLeft: 16,
        color: "blue"
    },
    buttonText: {
        ...TypoSkin.body2,
        color: 'black',
        marginLeft: 3
    },

    buttonTextAction: {
        ...TypoSkin.body2,
        color: 'blue',
        marginLeft: 3,
    },
    review: {
        flexDirection: 'row',
        padding: 10,
        borderBottomWidth: 0.2,
        borderBottomColor: '#00FFFF',
    },
    avatar: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 10,
    },
    reviewContent: {
        flex: 1,
    },
    name: {
        fontWeight: 'bold',
    },
    rating: {
        color: '#FFD700', // Màu vàng cho sao
    },
    description: {
        fontSize: 14,
        marginVertical: 5,
    },
    imagesProduct: {
        flexDirection: 'row',
        marginTop: 10,
        width: "100%",
        height: 100,
    },
    images: {
        flexDirection: 'row',
        marginTop: 10,

    },
    productImage: {
        width: 100,
        height: 100,
        marginRight: 10,
        borderRadius: 5,
    },
    reviewDetail: {
        flexDirection: "row",
        alignContent: "center",
    },
    avatarProduct: {
        width: 80,
        height: 80,
        borderRadius: 20,
        marginRight: 10,
    },
    imageDetail: {
        borderWidth: 5,
        borderRadius: 50,
        width: 50,
        height: 50,
        marginRight: 10,
        borderColor: "#F8F8FF",
        shadowColor: '#000000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 8,

    },
    avatarDetail: {
        width: 40,
        height: 40,
        padding: 20,
        borderRadius: 50,
    },
    tag: {
        fontSize: 15,
        color: '#555',
    },
    size: {
        fontSize: 12,
        color: '#888',
    },
});

export default ReviewItem;