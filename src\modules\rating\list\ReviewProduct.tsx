import React, {useEffect, useState} from 'react';
import {FlatList} from 'react-native-gesture-handler';
import {TypeMenuReview} from '../../../Config/Contanst';
import {DataController} from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import {ReviewDataDto, ReviewProductProps} from '../types';
import {View} from 'react-native';
import ReviewProductIteCard from '../card/CardReviewItem';
import {OrderDA} from '../../order/orderDA';

const ReviewProduct = (props: ReviewProductProps) => {
  const {type} = props;
  const [data, setData] = useState<ReviewDataDto[] | any[]>([]);
  const RatingController = new DataController('Rating');
  const RewardController = new DataController('Reward');
  const shopInfo = useSelectorShopState().data;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const orderDA = new OrderDA();
  useEffect(() => {
    setIsLoading(true);
    if (type == TypeMenuReview.Product) {
      callApiGetReview(2);
    } else {
      callApiGetReview(1);
    }
  }, [type]);

  let callApiGetReview = async (Type: number) => {
    if (Type == 2) {
      let resRating = await RatingController.getPatternList({
        query: `@ShopId:  {${shopInfo[0].Id}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
          ProductId: ['Id', 'Name', 'Description', 'Img', 'Content'],
        },
      });
      if (resRating && resRating.code === 200) {
        let arrayOrderId = resRating?.data?.map((item: any) => item.OrderId);
        let getRefund = await orderDA.getAllHistoryRewardByListId(arrayOrderId);
        let arrayData = resRating?.data?.map((item: any) => {
          return {
            ...item,
            Customer: resRating?.Customer?.find(
              (customer: any) => customer.Id == item.CustomerId,
            ),
            Product: resRating?.Product?.find(
              (product: any) => product.Id == item.ProductId,
            ),
            Refund: Array.isArray(getRefund)
              ? (
                  getRefund?.find(
                    (refund: any) => refund.OrderId == item.OrderId,
                  ) as any
                )?.value || 0
              : 0,
          };
        });
        setData(arrayData);
        setIsLoading(false);
      }
    }
  };

  if (data && data.length > 0) {
    return (
      <FlatList
        data={data}
        style={{flex: 1}}
        keyExtractor={(item, i) => `${i} ${item.Id}`}
        renderItem={({item, index}) =>
          ReviewProductIteCard({item, index}, type as string, isLoading)
        }
      />
    );
  } else {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <EmptyPage />
      </View>
    );
  }
};

export default ReviewProduct;
