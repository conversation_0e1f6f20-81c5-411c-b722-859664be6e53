/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {FDialog, FLoading} from 'wini-mobile-components';
import {
  useNavigation,
  useRoute,
  useFocusEffect,
} from '@react-navigation/native';
import {ScrollView} from 'react-native-gesture-handler';
import UserInfo from '../../components/shop/UserInfo';
import {TypeMenuShop} from '../../Config/Contanst';
import MenuShop from './component/MenuShop';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useDispatch, useSelector} from 'react-redux';
import {ProductActions} from '../../redux/reducers/ProductReducer';
import TreeAffiliate from './treeAffiliate';
import Profile from '../customer/profile';
import {RootScreen} from '../../router/router';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {RootState} from '../../redux/store/store';
import HaveShop from './HaveShop';
import NotShop from './NotShop';
import {shopStyles} from './styles';
import {OrderActions} from '../../redux/reducers/OrderReducer';

const Shop = () => {
  const route = useRoute<any>();
  const [select, setSelect] = useState(
    route?.params?.select ? route?.params?.select : TypeMenuShop.User,
  );
  const [numberOrder, setNumberOrder] = useState<number>(0);
  const shopInfo = useSelectorShopState().data;
  const {data: OrderData} = useSelector((state: RootState) => state.order);
  const [shop, setShop] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  //navi
  const navigation = useNavigation<any>();
  useEffect(() => {
    if (select == TypeMenuShop.Shop && shopInfo) {
      dispatch(ProductActions.getInforProduct(shopInfo[0]?.Id));
      setShop(shopInfo);
    }
    if (select === TypeMenuShop.Wallet) {
      navigation.push(RootScreen.MyWalletProfile);
    }
  }, [select, shopInfo]);

  useEffect(() => {
    if (OrderData && shopInfo[0]?.Id) {
      setNumberOrder(OrderData?.NewOrder?.number ?? 0);
    } else {
      setNumberOrder(0);
    }
  }, [OrderData, shopInfo]);
    
  // Tự động cập nhật numberOrder khi focus vào màn hình
  useFocusEffect(
    React.useCallback(() => {
      if (OrderData && shopInfo[0]?.Id) {
        setNumberOrder(OrderData?.NewOrder?.number ?? 0);
      } else {
        setNumberOrder(0);
      }
    }, [OrderData, shopInfo]),
  );
  return (
    <View style={shopStyles.containerManageShop}>
      <InforHeader title={select} showAction showBack={false} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={shopStyles.navigaterManageShop}>
        <FDialog ref={dialogRef} />
        <FLoading visible={loading} />
        <UserInfo />
        <MenuShop
          select={select}
          setSelect={setSelect}
          numberOrder={numberOrder}
        />
        {select == TypeMenuShop.Shop && shop && shop.length > 0 ? (
          <HaveShop shop={shop} />
        ) : select == TypeMenuShop.Afiliate ? (
          <TreeAffiliate />
        ) : select == TypeMenuShop.User ? (
          <Profile select={select} />
        ) : (
          <NotShop />
        )}
        <View style={shopStyles.footerManageShop} />
      </ScrollView>
    </View>
  );
};

export default Shop;
