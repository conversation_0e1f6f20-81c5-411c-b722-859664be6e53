import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Title} from '../../Config/Contanst';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import ShopPromortionComponent from './component/ShopPromortion';

const ShopPromotion = () => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={Title.ManagePromorion} />
      <ShopPromortionComponent />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
});

export default ShopPromotion;
