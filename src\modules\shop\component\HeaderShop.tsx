/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Image } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux/store/store';
import { shopStyles } from '../styles';
const HeaderBackground = () => {
  const [isVip, setIsVip] = useState(false);
  const { rankInfo } = useSelector((state: RootState) => state.customer);
  useEffect(() => {
    if (rankInfo?.achievedRank) {
      setIsVip(Number(rankInfo?.achievedRank?.Sort) > 1);
    }
  }, [rankInfo?.achievedRank]);
  return (
    <View style={shopStyles.containerHeaderShop}>
      {/* Header */}
      {isVip ? (
        <Image
          source={require('../../../assets/images/header_vip.png')}
          style={shopStyles.headerShopImage}
        />
      ) : (
        <Image
          source={require('../../../assets/images/header_group.png')}
          style={shopStyles.headerShopImage}
        />
      )}
    </View>
  );
};
export default HeaderBackground;


