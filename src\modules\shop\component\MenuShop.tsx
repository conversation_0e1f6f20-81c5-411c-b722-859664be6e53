/* eslint-disable react-native/no-inline-styles */
import React, { useRef } from 'react';
import { StyleSheet } from 'react-native';
import { TypeMenuShop } from '../../../Config/Contanst';
import { MenuNotAction } from '../../../components/Field/menu/ManageShopMenu/ManageShopMenu';
import iconSvg from '../../../svg/icon';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import { dialogCheckAcc } from '../../../Screen/Layout/mainLayout';
import { FDialog } from 'wini-mobile-components';
import { ScrollView } from 'react-native-gesture-handler';
import { RootScreen } from '../../../router/router';
import { useNavigation } from '@react-navigation/native';
import { shopStyles } from '../styles';
import { MenuShopProps } from '../types';
const MenuShop = (props: MenuShopProps) => {
  const { select, setSelect, numberOrder } = props;
  const customer = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const data = [
    {
      id: 0,
      title: 'Cá nhân',
      activeIcon: iconSvg.userIconAction,
      unActiveIcon: iconSvg.userIcon,
      type: TypeMenuShop.User,
    },
    {
      id: 1,
      title: 'Affiliate',
      activeIcon: iconSvg.afiliateAction,
      unActiveIcon: iconSvg.afiliate,
      type: TypeMenuShop.Afiliate,
    },
    {
      id: 2,
      title: 'Shop',
      activeIcon: iconSvg.shopAction,
      unActiveIcon: iconSvg.shop,
      type: TypeMenuShop.Shop,
    },
    {
      id: 3,
      title: 'Ví',
      activeIcon: iconSvg.walletAction,
      unActiveIcon: iconSvg.wallet,
      type: TypeMenuShop.Wallet,
    },
  ];
  return (
    <ScrollView
      showsHorizontalScrollIndicator={false}
      horizontal
      style={shopStyles.containerMenuShop}
      contentContainerStyle={{ gap: 8 }}>
      <FDialog ref={dialogRef} />
      {data.map(item => {
        if (item.type == TypeMenuShop.Wallet) {
          return (
            <MenuNotAction
              key={item.id}
              title={item.title}
              svgIcon={
                select === item.type ? item.activeIcon : item.unActiveIcon
              }
              selected={select === item.type}
              setSelect={(value: string) => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigation.push(RootScreen.MyWalletProfile);
              }}
              type={item.type}
              numberOrder={numberOrder as number}
            />
          );
        }
        return (
          <MenuNotAction
            key={item.id}
            title={item.title}
            svgIcon={select === item.type ? item.activeIcon : item.unActiveIcon}
            selected={select === item.type}
            setSelect={(value: string) => {
              if (!customer?.Id) {
                dialogCheckAcc(dialogRef);
                return;
              }
              setSelect(value);
            }}
            type={item.type}
            numberOrder={numberOrder as number}
          />
        );
      })}
    </ScrollView>
  );
};
export default MenuShop;
