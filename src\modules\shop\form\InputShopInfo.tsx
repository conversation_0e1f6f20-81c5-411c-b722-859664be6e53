import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Winicon, ComponentStatus, showSnackbar } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { StoreInfoFormProps } from '../../../components/dto/dto';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import { useDispatch } from 'react-redux';
import { validatePhoneNumber } from '../../../utils/validate';
import { randomGID } from '../../../utils/Utils';
import { ProductActions } from '../../../redux/reducers/ShoptReducer';
import { TextFieldForm } from '../../news/form/component-form';
import { FAddressPickerForm } from '../../Default/form/component-form';
import { shopStyles } from '../styles';
import { CustomDropdownNoBorder } from '../component/CustomDropdownNoBorder';
const StoreInfoForm = (props: StoreInfoFormProps) => {
  const { statusInput, data } = props;
  const methods = useForm({ shouldFocusError: false });
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const onSubmit = async (data: any) => {
    let res;
    if (data.Phone) {
      if (!/^(\+84|0)/.test(data.Phone)) {
        data.Phone = '0' + data.Phone; // Add 0 at the beginning
      }
      const val = validatePhoneNumber(data.Phone);
      if (val) methods.clearErrors('Phone');
      else {
        methods.setError('Phone', {
          message: 'Số điện thoại không hợp lệ',
        });
        return;
      }
    }

    if (data.Email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      var email = data?.Email.trim();
      let checkMail = emailRegex.test(email);
      if (checkMail) methods.clearErrors('Email');
      else {
        methods.setError('Email', {
          message: 'Email không hợp lệ',
        });
        return;
      }
    }
    const newShop = {
      Id: statusInput == 'edit' ? data.Id : randomGID(),
      Name: methods.watch('StoreName'),
      DateCreated: statusInput == 'edit' ? data.DateCreated : Date.now(),
      Mobile: methods.watch('Phone'),
      Status: methods.watch('Status'),
      Description: methods.watch('Description'),
      Address: data.Address,
      Email: data.Email,
      CustomerId: customer.Id,
    };
    if (statusInput == 'edit') {
      dispatch(ProductActions.editShop([newShop])).then((res: any) => {
        if (res.code == 200) {
          showSnackbar({
            message: 'Chỉnh sửa shop thành công',
            status: ComponentStatus.SUCCSESS,
          });
          dispatch(ProductActions.getInforShop(customer.Id));
          navigation.goBack();
        }
      });
    } else {
      dispatch(ProductActions.addShop([newShop])).then((res: any) => {
        if (res.code == 200) {
          showSnackbar({
            message: 'Đăng ký shop thành công',
            status: ComponentStatus.SUCCSESS,
          });
          // quay lại trang trước và cập nhật lại dữ liệu
          dispatch(ProductActions.getInforShop(customer.Id));
          navigation.goBack();
        }
      });
    }
  };
  useEffect(() => {
    methods.setValue('Id', data?.Id);
    methods.setValue('DateCreated', data?.DateCreated);
    methods.setValue('StoreName', data?.name);
    methods.setValue('Phone', data?.Phone);
    methods.setValue('Email', data?.Email);
    // methods.setValue("BusinessType", data?.BusinessType);
    methods.setValue('Description', data?.description);
    methods.setValue('Status', data?.Status || 0);
    methods.setValue('Address', data?.Address);
  }, [data]);

  const prefix = (icon: string) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          height: 32,
          width: 32,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Winicon
          src={icon}
          size={18}
          color={ColorThemes.light.neutral_text_subtitle_color}
        />
      </View>
    );
  };

  return (
    <KeyboardAvoidingView behavior="padding" enabled>
      <ScrollView style={shopStyles.containerInputShopInfo}>
        <KeyboardAvoidingView style={shopStyles.inputContainer}>
          <TextFieldForm
            control={methods.control}
            name="StoreName"
            placeholder="Nhập tên cửa hàng của bạn"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={shopStyles.textFieldStyleInputShopInfo}
            register={methods.register}
            required
            prefix={prefix('outline/user interface/home')}
          />
          <TextFieldForm
            control={methods.control}
            name="Phone"
            placeholder="Nhập Số điện thoại cửa hàng"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={shopStyles.textFieldStyleInputShopInfo}
            register={methods.register}
            type="phone-pad"
            required
            prefix={prefix('outline/user interface/mirror-tablet-phone')}
          />
          <TextFieldForm
            control={methods.control}
            name="Email"
            placeholder="Email"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={shopStyles.textFieldStyleInputShopInfo}
            register={methods.register}
            required
            prefix={prefix('outline/user interface/email')}
          />
          <TextFieldForm
            control={methods.control}
            name="Description"
            placeholder="Giới thiệu ngắn"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={shopStyles.textFieldStyleInputShopInfo}
            register={methods.register}
            required
            prefix={prefix('fill/multimedia/audio-description')}
            multiline={true}
          />
          <View style={shopStyles.selectFieldStyleInputShopInfo}>
            <Winicon
              src="outline/user interface/gear"
              size={18}
              color={ColorThemes.light.neutral_text_subtitle_color}
            />
            <CustomDropdownNoBorder
              control={methods.control}
              errors={methods.formState.errors}
              style={shopStyles.dropdownInputShopInfo}
              required
              placeholder="Chọn trạng thái "
              name="Status"
              options={[
                { id: 0, name: 'Khởi tạo' },
                { id: 1, name: 'Hoạt động' },
              ]}
            />
          </View>

          {!statusInput && (
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              prefix={
                <View
                  style={shopStyles.selectMapInputShopInfo}>
                  <Winicon
                    src="fill/user interface/password"
                    size={18}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                </View>
              }
              placeholder="Nhập địa chỉ của bạn"
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
              textFieldStyle={{
                paddingLeft: 8,
                gap: 12,
                height: 32,
                borderWidth: 0,
              }}
            />
          )}
          <TouchableOpacity
            style={shopStyles.buyButtonInputShopInfo}
            onPress={methods.handleSubmit(onSubmit)}>
            <Text style={shopStyles.buyActionButtonInputShopInfo}>Hoàn Thành</Text>
          </TouchableOpacity>
        </KeyboardAvoidingView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};
export default StoreInfoForm;
