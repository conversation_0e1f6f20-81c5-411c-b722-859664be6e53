import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {NewsItem} from '../models/news';
import {getImage} from './rootAction';
import { fetchComments, fetchLikes, fetchLikesCount } from '../../modules/community/reducers/newsFeedReducer';

export const newsAction = {
  fetch: async (config: {
    page?: number;
    size?: number;
    searchRaw?: string;
    sortby?: {prop: string; direction?: 'ASC' | 'DESC'}[];
  }) => {
    try {
      const controller = new DataController('News');
      const query: any = {
        page: config.page ?? 1,
        size: config.size ?? 10,
      };
      if (config.searchRaw) query.searchRaw = config.searchRaw;
      if (config.sortby) query.sortby = config.sortby;
      const res = await controller.aggregateList(query);
      if (res.code === 200) {
        // get comments and likes
        let listData = res.data;
        const listComments = await fetchComments(
          listData.map((item: any) => item.Id), true,
        );
        const listLikes = await fetchLikesCount(
          listData.map((item: any) => item.Id), true,
        );
        listData.forEach((item: any) => {
          item.Comment = listComments.find(
            (comment: any) => comment.NewsId === item.Id,
          )?.CommentsCount;
          item.LikesCount = listLikes.find(
            (like: any) => like.NewsId === item.Id,
          )?.LikesCount;
        });
        listData = await getImage({items: listData});
        return {
          data: listData,
          totalCount: res.totalCount,
        };
      }
      return {
        data: [],
        totalCount: 0,
      };
    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return {
        data: [],
        totalCount: 0,
      };
    }
  },
  fetchById: async (id: string) => {
    try {
      const controller = new DataController('News');
      const res = await controller.getById(id);
      if (res.code === 200) {
        let data = res.data;
        data = await getImage({items: [data]});
        return data[0];
      }
      return null;
    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return null;
    }
  },
};

const fetchNews = createAsyncThunk<
  NewsItem[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('news/fetchNews', async (config, thunkAPI: any) => {
  const controller = new DataController('News');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 10,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const listData = await getImage({items: res.data});
      return {
        data: listData,
        totalCount: res.totalCount,
      };
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const loadMoreNews = createAsyncThunk<
  NewsItem[],
  {page?: number; status?: number} | undefined,
  {state: RootState}
>('news/fetchData', async (config, thunkAPI: any) => {
  const controller = new DataController('News');
  try {
    const res = await controller.aggregateList({
      page: config?.page ?? 1,
      size: 10,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
    });
    if (res.code === 200) {
      const listData = await getImage({items: res.data});
      return listData;
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export {fetchNews, loadMoreNews};
