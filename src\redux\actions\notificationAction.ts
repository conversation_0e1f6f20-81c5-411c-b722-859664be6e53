import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {RootState} from '../store/store';
import {DataController} from '../../base/baseController';
import {NotificationItem} from '../models/notification';

const notificationDa = {
  get: async ({
    config,
    searchRaw,
  }: {
    config: {page?: number; size?: number; status?: number};
    searchRaw: string;
    cusId: string;
  }) => {
    const controller = new DataController('Notification');

    try {
      const searchParams = {
        page: config.page ?? 1,
        size: config.size ?? 10,
        searchRaw,
        sortby: [{prop: 'DateCreated', direction: 'DESC' as const}],
      };

      const res = await controller.aggregateList(searchParams);

      if (res.code === 200) {
        return res.data || [];
      }
    } catch (e: any) {
      const errorMessage = e.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return [];
    }
  },
  update: async ({data}: {data: any}) => {
    const controller = new DataController('Notification');

    try {
      const res = await controller.edit(data);

      if (res.code === 200) {
        return res.data || [];
      }
      return res;
    } catch (e: any) {
      const errorMessage = e.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return [];
    }
  },
};

const fetchNotifications = createAsyncThunk<
  NotificationItem[],
  {page?: number; type: number[]; status?: number} | undefined,
  {state: RootState}
>('notification/fetchNotifications', async (config: any, thunkAPI: any) => {
  const cusId = thunkAPI.getState().customer.data?.Id;

  const data = await notificationDa.get({
    config,
    searchRaw: `@CustomerId:{${cusId}} @Type:(${config.type.join('|')})${
      config.status !== undefined ? ` @Status:[${config.status}]` : ''
    }`,
    cusId,
  });

  return data;
});

const readAllNotification = createAsyncThunk<
  NotificationItem[],
  number[],
  {state: RootState}
>('notification/readAllNotification', async (type: number[], thunkAPI: any) => {
  const cusId = thunkAPI.getState().customer.data?.Id;

  const data = await notificationDa.get({
    config: {page: 1, size: 1000},
    searchRaw: `@CustomerId:{${cusId}} @Type:(${type.join('|')})`,
    cusId,
  });

  const edits = data.map((item: any) => {
    return {
      ...item,
      Status: 1,
    };
  });

  const res = await notificationDa.update({
    data: edits,
  });

  return res;
});

export {fetchNotifications, readAllNotification};
