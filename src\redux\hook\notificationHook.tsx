import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../store/store';
import {setData} from '../reducers/notificationReducer';
import type {notificationSimpleResponse} from '../reducers/notificationReducer';

export const useNotificationHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: <K extends keyof notificationSimpleResponse>(
      stateName: K,
      data: notificationSimpleResponse[K],
    ) => {
      dispatch(setData({stateName, data}));
    },
  };

  return action;
};

export const useSelectorNotificationState = () => {
  return useSelector((state: RootState) => state.notification);
};

export const useNotificationBadgeCount = () => {
  return useSelector((state: RootState) => state.notification.badgeCount);
};
