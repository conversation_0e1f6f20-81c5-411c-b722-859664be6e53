import {createSlice, PayloadAction, Dispatch} from '@reduxjs/toolkit';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
import {randomGID} from '../../utils/Utils';
import {DataController} from '../../base/baseController';
import store from '../store/store';
import {CartItem} from '../types/cartTypes';

// Định nghĩa kiểu dữ liệu cho state của giỏ hàng
interface CartState {
  items: CartItem[];
  loading: boolean;
}

// Khởi tạo state ban đầu
const initialState: CartState = {
  items: [],
  loading: false,
};

// Tạo slice cho giỏ hàng
export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    // Thêm sản phẩm vào giỏ hàng
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const existingItemIndex = state.items.findIndex(
        item => item.ProductId === action.payload.ProductId,
      );

      if (existingItemIndex >= 0) {
        // Nếu sản phẩm đã tồn tại, tăng số lượng
        state.items[existingItemIndex].Quantity += action.payload.Quantity;
      } else {
        // Nếu sản phẩm chưa tồn tại, thêm mới
        state.items.push(action.payload);
      }
    },
    loadCart: (state, action: PayloadAction<CartItem[]>) => {
      state.items = action.payload;
      state.loading = false;
    },

    // Cập nhật số lượng sản phẩm
    updateQuantity: (
      state,
      action: PayloadAction<{id: string; quantity: number}>,
    ) => {
      const {id, quantity} = action.payload;
      const itemIndex = state.items.findIndex(item => item.id === id);

      if (itemIndex >= 0) {
        state.items[itemIndex].Quantity = quantity;
      }
      CartActions.saveCartToStorage(state.items);
    },

    // Xóa sản phẩm khỏi giỏ hàng
    removeFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      CartActions.saveCartToStorage(state.items);
    },

    // Cập nhật trạng thái chọn của sản phẩm
    toggleSelectItem: (state, action: PayloadAction<string>) => {
      const itemIndex = state.items.findIndex(
        item => item.id === action.payload,
      );

      if (itemIndex >= 0) {
        state.items[itemIndex].selected = !state.items[itemIndex].selected;
      }
    },

    // Chọn tất cả sản phẩm
    selectAllItems: (state, action: PayloadAction<boolean>) => {
      state.items = state.items.map(item => ({
        ...item,
        selected: action.payload,
      }));
    },

    // Chọn tất cả sản phẩm của một cửa hàng
    selectStoreItems: (
      state,
      action: PayloadAction<{storeId: string; selected: boolean}>,
    ) => {
      const {storeId, selected} = action.payload;
      state.items = state.items.map(item =>
        item.ShopId === storeId ? {...item, selected} : item,
      );
    },

    // Xóa tất cả sản phẩm đã chọn
    removeSelectedItems: state => {
      state.items = state.items.filter(item => !item.selected);
      CartActions.saveCartToStorage(state.items);
    },

    // Xóa các sản phẩm theo danh sách ID
    removeItemsByIds: (state, action: PayloadAction<string[]>) => {
      const idsToRemove = new Set(action.payload);
      state.items = state.items.filter(item => !idsToRemove.has(item.id));
      CartActions.saveCartToStorage(state.items);
    },

    // Xóa tất cả sản phẩm
    clearCart: state => {
      state.items = [];
      CartActions.saveCartToStorage(state.items);
    },

    // Đặt trạng thái loading
    setLoading: state => {
      state.loading = true;
    },

    // Đặt toàn bộ giỏ hàng
    setCart: (state, action: PayloadAction<CartItem[]>) => {
      state.items = action.payload;
    },
  },
});

// Export các actions
export const {
  addToCart,
  updateQuantity,
  removeFromCart,
  toggleSelectItem,
  selectAllItems,
  selectStoreItems,
  removeSelectedItems,
  removeItemsByIds,
  clearCart,
  setLoading,
  setCart,
  loadCart,
} = cartSlice.actions;

// Export reducer
export default cartSlice.reducer;

// Các thunk actions
export class CartActions {
  // Thêm sản phẩm vào giỏ hàng
  static addItemToCart =
    (product: any, quantity: number = 1) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(setLoading());

        // Kiểm tra số lượng tồn kho của sản phẩm
        const productController = new DataController('Product');
        const productData = await productController.getById(product.Id);
        console.log('productData', productData);

        if (!productData || !productData?.data) {
          showSnackbar({
            message: 'Không tìm thấy thông tin sản phẩm',
            status: ComponentStatus.ERROR,
          });
          return;
        }

        const stockQuantity = productData?.data?.InStock || 0;

        // Kiểm tra tồn kho có đủ hay không
        if (stockQuantity < 1) {
          console.log('check-ádasdsadsada');
          showSnackbar({
            message: 'Sản phẩm hiện đã hết hàng',
            status: ComponentStatus.ERROR,
          });
          return;
        }

        // Lấy state hiện tại của giỏ hàng
        const state = store.getState();
        const tempCartItems = state.cart.items;

        // Kiểm tra số lượng hiện có trong giỏ hàng
        const existingCartItem = tempCartItems.find(
          item => item.ProductId === product.Id,
        );
        const currentCartQuantity = existingCartItem
          ? existingCartItem.Quantity
          : 0;
        const totalQuantityAfterAdd = currentCartQuantity + quantity;

        // Kiểm tra xem tổng số lượng có vượt quá tồn kho không
        if (totalQuantityAfterAdd > stockQuantity) {
          const availableQuantity = stockQuantity - currentCartQuantity;
          if (availableQuantity <= 0) {
            showSnackbar({
              message: 'Sản phẩm đã đạt giới hạn tồn kho trong giỏ hàng',
              status: ComponentStatus.ERROR,
            });
          } else {
            showSnackbar({
              message: `Chỉ có thể thêm tối đa ${availableQuantity} sản phẩm nữa`,
              status: ComponentStatus.ERROR,
            });
          }
          return;
        }

        const shopIds = [
          ...tempCartItems.map(item => item.ShopId),
          product.ShopId,
        ];

        const uniqueShopIds = Array.from(new Set(shopIds));
        const shopController = new DataController('Shop');
        const shopsData = await shopController.getPatternList({
          page: 1,
          size: 100,
          query: `@Id: {${uniqueShopIds.join(' | ')}}`,
          pattern: {
            CustomerId: ['Id', 'Name', 'AvatarUrl'],
          },
        });
        const cartItems = tempCartItems.map(item => {
          const shop = shopsData.data.find(
            (shop: any) => shop.Id === item.ShopId,
          );
          return {
            ...item,
            ShopName: shop?.Name,
            ShopAvatar: shopsData.Customer?.find(
              (customer: any) => customer.Id === shop.CustomerId,
            )?.AvatarUrl,
          };
        });

        // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
        // Sản phẩm được xem là giống nhau nếu có cùng productId, color và size
        const existingItem = cartItems.find(
          item => item.ProductId === product.Id,
        );
        if (existingItem) {
          // Nếu sản phẩm đã tồn tại, cập nhật số lượng
          dispatch(
            updateQuantity({
              id: existingItem.id,
              quantity: existingItem.Quantity + quantity,
            }),
          );
        } else {
          const shop = shopsData.data.find(
            (shop: any) => shop.Id === product.ShopId,
          );
          // Nếu sản phẩm chưa tồn tại, tạo mới và thêm vào giỏ hàng
          const cartItem: CartItem = {
            id: randomGID(),
            ProductId: product.Id,
            Name: product.Name,
            Price: product.Price,
            // originalPrice: product.OriginalPrice || product.Price,
            Quantity: quantity,
            Img: product.Img,
            ShopId: product.ShopId,
            ShopName: shop?.Name,
            ShopAvatar: shopsData.Customer?.find(
              (customer: any) => customer.Id === shop.CustomerId,
            )?.AvatarUrl,
            Discount: product.Discount,
            selected: true,
            dateAdded: Date.now(),
            CategoryId: product.CategoryId,
            IsFreeShip: product.IsFreeShip,
          };

          dispatch(addToCart(cartItem));
        }

        // Lưu giỏ hàng vào AsyncStorage
        CartActions.saveCartToStorage(store.getState().cart.items);

        showSnackbar({
          message: 'Đã thêm sản phẩm vào giỏ hàng',
          status: ComponentStatus.SUCCSESS,
        });
      } catch (error) {
        console.error('Error adding item to cart:', error);
        showSnackbar({
          message: 'Không thể thêm sản phẩm vào giỏ hàng',
          status: ComponentStatus.ERROR,
        });
      }
    };

  // Lưu giỏ hàng vào AsyncStorage
  static saveCartToStorage = async (cartItems: CartItem[]) => {
    try {
      await saveDataToAsyncStorage(
        StorageContanst.CartItems,
        JSON.stringify(cartItems),
      );
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  };

  // Tải giỏ hàng từ AsyncStorage
  static loadCartFromStorage = () => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading());
      const cartItemsJson = await getDataToAsyncStorage(
        StorageContanst.CartItems,
      );
      if (cartItemsJson) {
        const cartItems = JSON.parse(cartItemsJson as string) as CartItem[];
        dispatch(setCart(cartItems));
      } else {
        dispatch(setCart([]));
      }
    } catch (error) {
      console.error('Error loading cart from storage:', error);
      dispatch(setCart([]));
      showSnackbar({
        message: 'Không thể tải giỏ hàng',
        status: ComponentStatus.ERROR,
      });
    }
  };
}
