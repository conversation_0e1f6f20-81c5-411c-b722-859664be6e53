import {configureStore} from '@reduxjs/toolkit';
// import customerReducer from '../reducers/user/reducer'
import locationReducer from '../location-reducer';
import customerReducer from '../reducers/CustomerReducer';
import cartReducer from '../reducers/CartReducer';
import shopReducer from '../reducers/ShoptReducer';
import orderReducer from '../reducers/OrderReducer';
import productReducer from '../reducers/ProductReducer';
import notificationReducer from '../reducers/notificationReducer';
import newsEventReducer from '../reducers/NewsEventReducer';
import newsReducer from '../reducers/NewsReducer';
import categoryReducer from '../reducers/CategoryReducer';
import favoriteProductReducer from '../reducers/FavoriteProductReducer';
import chatReducer from '../reducers/ChatReducer';
import productByCategoryReducer from '../reducers/ProductByCategoryReducer';
import brandReducer from '../reducers/BrandReducer';
import newsFeedReducer from '../../modules/community/reducers/newsFeedReducer';
import postCommentsReducer from '../../modules/community/reducers/postCommentsReducer';
import postBackgroundReducer from '../reducers/postBackgroundReducer';
import myFeedReducer from '../../modules/community/reducers/myFeedReducer';

export const store = configureStore({
  reducer: {
    location: locationReducer,
    customer: customerReducer,
    notification: notificationReducer,
    cart: cartReducer,
    shop: shopReducer,
    order: orderReducer,
    product: productReducer,
    newsEvent: newsEventReducer,
    category: categoryReducer,
    news: newsReducer,
    favoriteProduct: favoriteProductReducer,
    chat: chatReducer,
    productByCategory: productByCategoryReducer,
    brand: brandReducer,
    newsFeed: newsFeedReducer,
    myFeed: myFeedReducer,
    postComments: postCommentsReducer,
    postBackground: postBackgroundReducer,
  },
});

export default store;
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
