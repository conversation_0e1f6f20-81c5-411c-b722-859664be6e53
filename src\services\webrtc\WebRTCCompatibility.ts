/**
 * WebRTC Compatibility Helper
 * Handles compatibility issues between different versions of react-native-webrtc
 */

import {
  RTCPeerConnection,
  RTCSessionDescription,
  RTCIceCandidate,
  MediaStream,
  mediaDevices,
} from 'react-native-webrtc';

/**
 * WebRTC Compatibility utilities
 */
export class WebRTCCompatibility {
  
  /**
   * Add stream/tracks to peer connection with compatibility
   */
  static addStreamToPeerConnection(
    peerConnection: RTCPeerConnection,
    stream: MediaStream
  ): void {
    try {
      // Try modern approach first (addTrack)
      if (typeof (peerConnection as any).addTrack === 'function') {
        stream.getTracks().forEach(track => {
          (peerConnection as any).addTrack(track, stream);
        });
        console.log('✅ Added tracks using addTrack method');
      }
      // Fallback to legacy approach (addStream)
      else if (typeof (peerConnection as any).addStream === 'function') {
        (peerConnection as any).addStream(stream);
        console.log('✅ Added stream using addStream method');
      }
      else {
        throw new Error('Neither addTrack nor addStream is available');
      }
    } catch (error) {
      console.error('❌ Error adding stream to peer connection:', error);
      throw error;
    }
  }

  /**
   * Set up remote stream event handler with compatibility
   */
  static setupRemoteStreamHandler(
    peerConnection: RTCPeerConnection,
    onRemoteStream: (stream: MediaStream) => void
  ): void {
    try {
      // Try modern approach first (ontrack)
      if ('ontrack' in peerConnection) {
        (peerConnection as any).ontrack = (event: any) => {
          console.log('📡 Remote track received (ontrack)');
          if (event.streams && event.streams[0]) {
            onRemoteStream(event.streams[0]);
          }
        };
        console.log('✅ Set up remote stream handler using ontrack');
      }
      // Fallback to legacy approach (onaddstream)
      else if ('onaddstream' in peerConnection) {
        (peerConnection as any).onaddstream = (event: any) => {
          console.log('📡 Remote stream received (onaddstream)');
          if (event.stream) {
            onRemoteStream(event.stream);
          }
        };
        console.log('✅ Set up remote stream handler using onaddstream');
      }
      else {
        throw new Error('Neither ontrack nor onaddstream is available');
      }
    } catch (error) {
      console.error('❌ Error setting up remote stream handler:', error);
      throw error;
    }
  }

  /**
   * Create offer with compatibility options
   */
  static async createOffer(peerConnection: RTCPeerConnection): Promise<RTCSessionDescription> {
    try {
      // Modern approach
      const offer = await peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      });
      
      console.log('✅ Created offer successfully');
      return offer;
    } catch (error) {
      console.error('❌ Error creating offer:', error);
      throw error;
    }
  }

  /**
   * Create answer with compatibility options
   */
  static async createAnswer(peerConnection: RTCPeerConnection): Promise<RTCSessionDescription> {
    try {
      const answer = await peerConnection.createAnswer();
      console.log('✅ Created answer successfully');
      return answer;
    } catch (error) {
      console.error('❌ Error creating answer:', error);
      throw error;
    }
  }

  /**
   * Get user media with enhanced constraints
   */
  static async getUserMedia(audioOnly: boolean = true): Promise<MediaStream> {
    try {
      const constraints = audioOnly ? {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
        },
        video: false,
      } : {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
        video: {
          width: { min: 640, ideal: 1280, max: 1920 },
          height: { min: 480, ideal: 720, max: 1080 },
          frameRate: { min: 15, ideal: 30, max: 30 },
        },
      };

      const stream = await mediaDevices.getUserMedia(constraints as any);
      console.log('✅ Got user media successfully');
      return stream;
    } catch (error) {
      console.error('❌ Error getting user media:', error);
      throw error;
    }
  }

  /**
   * Check WebRTC support and capabilities
   */
  static checkWebRTCSupport(): {
    isSupported: boolean;
    capabilities: {
      hasGetUserMedia: boolean;
      hasPeerConnection: boolean;
      hasAddTrack: boolean;
      hasOnTrack: boolean;
    };
  } {
    const capabilities = {
      hasGetUserMedia: typeof mediaDevices?.getUserMedia === 'function',
      hasPeerConnection: typeof RTCPeerConnection === 'function',
      hasAddTrack: false,
      hasOnTrack: false,
    };

    // Check addTrack support
    try {
      const tempPC = new RTCPeerConnection();
      capabilities.hasAddTrack = typeof (tempPC as any).addTrack === 'function';
      capabilities.hasOnTrack = 'ontrack' in tempPC;
      tempPC.close();
    } catch (error) {
      console.warn('Could not create temporary peer connection for capability check');
    }

    const isSupported = capabilities.hasGetUserMedia && capabilities.hasPeerConnection;

    console.log('📊 WebRTC Support Check:', { isSupported, capabilities });
    return { isSupported, capabilities };
  }

  /**
   * Get recommended ICE servers
   */
  static getRecommendedIceServers(): any[] {
    return [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
      { urls: 'stun:stun3.l.google.com:19302' },
      { urls: 'stun:stun4.l.google.com:19302' },
    ];
  }

  /**
   * Get peer connection configuration
   */
  static getPeerConnectionConfig(): any {
    return {
      iceServers: this.getRecommendedIceServers(),
      iceCandidatePoolSize: 10,
    };
  }

  /**
   * Safely close peer connection
   */
  static closePeerConnection(peerConnection: RTCPeerConnection | null): void {
    if (peerConnection) {
      try {
        // Remove event listeners
        (peerConnection as any).onicecandidate = null;
        (peerConnection as any).ontrack = null;
        (peerConnection as any).onaddstream = null;
        (peerConnection as any).oniceconnectionstatechange = null;
        
        // Close connection
        peerConnection.close();
        console.log('✅ Peer connection closed safely');
      } catch (error) {
        console.error('❌ Error closing peer connection:', error);
      }
    }
  }

  /**
   * Safely stop media stream
   */
  static stopMediaStream(stream: MediaStream | null): void {
    if (stream) {
      try {
        stream.getTracks().forEach(track => {
          track.stop();
        });
        console.log('✅ Media stream stopped safely');
      } catch (error) {
        console.error('❌ Error stopping media stream:', error);
      }
    }
  }

  /**
   * Format call duration
   */
  static formatCallDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Generate unique ID
   */
  static generateUniqueId(prefix: string = 'id'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

export default WebRTCCompatibility;
